import { change } from '../dbScript'

change(async db => {
	await db.createSchema('keycloak')

	await db.createSchema('public')
})

change(async db => {
	await db.createEnum('public.banking_billet_status_enum', [
		'RECEBIDO',
		'A_RECEBER',
		'MARCADO_RECEBIDO',
		'ATRASADO',
		'CANCELADO',
		'EXPIRADO',
		'FALHA_EMISSAO',
		'EM_PROCESSAMENTO',
	])

	await db.createEnum('public.banking_billet_types_enum', ['SIMPLES', 'PARCELADO', 'RECORRENTE'])

	await db.createEnum('public.banking_pix_account_types_enum', ['Conta Corrente', 'Conta Poupança', 'Conta Salário', 'Conta de Pagamento'])

	await db.createEnum('public.banking_pix_types_enum', ['<PERSON>ve', '<PERSON><PERSON>', 'Pix Copia e Cola'])

	await db.createEnum('public.banking_pix_status_enum', ['Criado', 'Enviado para Processamento', 'Processado', 'Confirmado'])

	await db.createEnum('public.certidoes_type', ['Federal', 'Estadual', 'Municipal'])

	await db.createEnum('public.certidoes_result', ['Negativa', 'Positiva', 'Positiva com efeitos de negativa'])

	await db.createEnum('public.classifications_flows_tasks_date_format', [
		'DD/MM/AAAA',
		'DD.MM.AAAA',
		'MM/AAAA',
		'AAAA',
		'AAAA MM',
		'AAAA/MM',
	])

	await db.createEnum('public.classifications_flows_tasks_identifier', ['CNPJ', 'CNPJ/CPF', 'CPF', 'IE', 'IM'])

	await db.createEnum('public.classifications_flows_taxes_date_format', [
		'DD/MM/AAAA',
		'DD.MM.AAAA',
		'MM/AAAA',
		'AAAA',
		'AAAA MM',
		'AAAA/MM',
	])

	await db.createEnum('public.classifications_flows_taxes_identifier', ['CNPJ', 'CNPJ/CPF', 'CPF', 'IE'])

	await db.createEnum('public.classifications_flows_taxes_guia', [
		'Guia de IRPJ',
		'Guia de CRF',
		'Guia de CSLL',
		'Guia de PIS',
		'Guia de COFINS',
		'Guia de ISS',
		'Guia de ISS Retido',
		'Guia de ICMS',
		'Guia de INSS',
		'Guia de INSS 13º',
		'Guia de FGTS',
		'Guia de FGTS 13º',
		'Guia de Simples Nacional',
		'Guia de IRRF',
		'Guia de IPI',
		'Guia de Parcelamento',
		'Guia de ICMS Recomposição de Alíquota',
	])

	await db.createEnum('public.classification_rules_type', ['Rule', 'Anti-Rule'])

	await db.createEnum('public.cnpj_matriz_filial_enum', ['MATRIZ', 'FILIAL'])

	await db.createEnum('public.cnpj_situacao_cadastral_enum', ['Ativa', 'Ativa Não Regular', 'Baixada', 'Inapta', 'Nula', 'Suspensa'])

	await db.createEnum('public.cte_type', ['Emitente', 'Remetente', 'Expedidor', 'Recebedor', 'Destinatário', 'Outros'])

	await db.createEnum('public.cte_events_event_type', [
		'110110',
		'110111',
		'110113',
		'110160',
		'110170',
		'110180',
		'110181',
		'310610',
		'310611',
		'310620',
		'510620',
		'240130',
		'240131',
		'240140',
		'240160',
		'240170',
		'240180',
		'240181',
		'440130',
		'440140',
		'440150',
		'440160',
		'610110',
		'610111',
		'110190',
	])

	await db.createEnum('public.cte_os_type', ['Emitente', 'Tomador'])

	await db.createEnum('public.customers_unit_enum', ['headquarters', 'branch'])

	await db.createEnum('public.customers_partners_qualification_enum', [
		'Administrador',
		'Administrador Judicial',
		'Administrador Residente ou Domiciliado no Exterior',
		'Candidato a Cargo Político Eletivo',
		'Conselheiro de Administração',
		'Conselheiro de Administração Res. ou Dom. no Exterior',
		'Cotas em Tesouraria',
		'Curador',
		'Cônsul',
		'Cônsul Honorário',
		'Diplomata',
		'Diretor',
		'Diretor Residente ou Domiciliado no Exterior',
		'Empresário',
		'Fundador',
		'Fundador Residente ou Domiciliado no Exterior',
		'Interventor',
		'Inventariante',
		'Liquidante',
		'Ministro de Estado das Relações Exteriores',
		'Mãe',
		'Oficial de Registro',
		'Pai',
		'Presidente',
		'Presidente Residente ou Domiciliado no Exterior',
		'Procurador',
		'Produtor Rural',
		'Representante das Instituições Extraterritoriais',
		'Representante de Organização Internacional',
		'Responsável',
		'Responsável Indígena',
		'Sociedade Consorciada',
		'Sociedade Filiada',
		'Síndico (Condomínio)',
		'Sócio',
		'Sócio Capitalista',
		'Sócio Comanditado',
		'Sócio Comanditado Residente no Exterior',
		'Sócio Comanditário',
		'Sócio Comanditário Incapaz',
		'Sócio Comanditário Pessoa Física Residente no Exterior',
		'Sócio Comanditário Pessoa Jurídica Domiciliado no Exterior',
		'Sócio Ostensivo',
		'Sócio Pessoa Física Residente no Brasil',
		'Sócio Pessoa Física Residente ou Domiciliado no Exterior',
		'Sócio Pessoa Jurídica Domiciliado no Brasil',
		'Sócio Pessoa Jurídica Domiciliado no Exterior',
		'Sócio com Capital',
		'Sócio de Indústria',
		'Sócio ou Acionista Incapaz ou Rel. Incapaz (exceto menor)',
		'Sócio ou Acionista Menor (Assistido/Representado)',
		'Sócio sem Capital',
		'Sócio-Administrador',
		'Sócio-Administrador Residente ou Domiciliado no Exterior',
		'Sócio-Gerente',
		'Tabelião',
		'Titular PF Incapaz ou Relativamente Incapaz (exceto menor)',
		'Titular PF Menor (Assistido/Representado)',
		'Titular PF Residente ou Domiciliado no Brasil',
		'Titular PF Residente ou Domiciliado no Exterior',
		'Titular Pessoa Jurídica Domiciliada no Brasil',
		'Titular Pessoa Jurídica Domiciliada no Exterior',
		'Titular de Empresa Individual Imobiliária',
		'Tutor',
	])

	// customers_secrets_types_enum removed - now using vault.secrets

	await db.createEnum('public.customers_tax_numbers_type_enum', ['CNPJ', 'CPF', 'IE', 'NIRE', 'CEI', 'Cartório'])

	await db.createEnum('public.customers_tax_passwords_type_enum', [
		'Declaração municipal (DES) ou semelhante',
		'Declaração estadual (SIARE) ou semelhante',
		'Simples Nacional',
		'FAP',
		'Empregador Web',
		'Gov.BR',
		'REDESIM',
	])

	await db.createEnum('public.tax_timeline_tax_enum', [
		'Condomínio Edilício',
		'Empregador Doméstico',
		'Imune',
		'Inativa',
		'Isenta',
		'Lucro Presumido',
		'Lucro Real Mensal',
		'Lucro Real Trimestral',
		'MEI',
		'Pessoa Física com Atividade Econômica',
		'Pessoa Física Equiparada a Pessoa Jurídica',
		'Produtor Rural',
		'Simples Com Desoneração da Folha',
		'Simples Nacional - 1ª a 5ª Faixas',
		'Simples Nacional - 6ª Faixa',
	])

	await db.createEnum('public.tax_documents_pa_enum', ['AN', 'TR', 'ME', 'QZ', 'DC', 'SM', 'DI'])

	await db.createEnum('public.darf_codes_group_enum', [
		'IRPJ',
		'IRRF',
		'IPI',
		'IOF',
		'CSLL',
		'PIS',
		'COFINS',
		'CRF pelas PJ de Direito Privado',
		'IRPJ e CRF / Art. 34 da Lei nº 10.833/2003',
		'Contribuições Previdenciárias',
		'CIDE',
		'RET – Pagamento Unificado de Tributos',
	])

	await db.createEnum('public.documents_frequencies_enum', ['Anual', 'Mensal'])

	await db.createEnum('public.documents_document_types_enum', ['Cadastrais', 'Contábeis', 'Fiscais', 'Trabalhistas'])

	await db.createEnum('public.email_messages_recipients_type_enum', ['to', 'cc', 'bcc'])

	await db.createEnum('public.files_type_enum', ['xml', 'pdf', 'docx', 'xlsx', 'png', 'pptx'])

	await db.createEnum('public.nfe_type_enum', ['Entrada', 'Saída', 'Transportadora'])

	await db.createEnum('public.nfe_protocol_status_enum', ['100', '101', '204', '301', '302', '303'])

	await db.createEnum('public.nfe_is_denied_enum', ['Sim', 'Não'])

	await db.createEnum('public.nfe_is_tenant_contabilidade_authorized_enum', ['Sim', 'Não'])

	await db.createEnum('public.nfse_type_enum', ['Prestado', 'Tomado', 'Intermediário'])

	await db.createEnum('public.nfse_is_canceled_enum', ['Sim', 'Não'])

	await db.createEnum('public.nfse_simples_nacional_enum', [
		'Não Optante',
		'Optante - Microempreendedor Individual (MEI)',
		'Optante - Microempresa ou Empresa de Pequeno Porte (ME/EPP)',
		'Desconhecido',
	])

	await db.createEnum('public.nfse_tomador_is_pessoa_juridica_enum', ['Sim', 'Não'])

	await db.createEnum('public.nfse_origin_enum', ['NFS-e Nacional', 'Invoicy'])

	await db.createEnum('public.nfse_events_types_enum', [
		'e101101',
		'e101103',
		'e105102',
		'e105104',
		'e105105',
		'e305101',
		'e203202',
		'e203206',
	])

	await db.createEnum('public.other_incomes_types_enum', [
		'1',
		'2',
		'4',
		'6',
		'8',
		'9',
		'10',
		'11',
		'12',
		'13',
		'14',
		'15',
		'16',
		'17',
		'18',
		'19',
		'20',
		'20.01',
		'22',
		'23',
		'24',
		'25',
		'25.01',
		'25.02',
		'26',
	])

	await db.createEnum('public.permissao', ['R', 'W'])

	await db.createEnum('public.destinacao', [
		'00',
		'01',
		'02',
		'03',
		'04',
		'05',
		'06',
		'07',
		'07-#@9000#@',
		'07-#@9001#@',
		'07-#@9002#@',
		'07-#@9003#@',
		'07-#@9004#@',
		'07-#@9005#@',
		'07-#@9006#@',
		'07-#@9007#@',
		'07-#@9008#@',
		'07-#@9009#@',
		'07-#@9010#@',
		'07-#@9011#@',
		'07-#@9012#@',
		'07-#@9013#@',
		'07-#@9014#@',
		'07-#@9015#@',
		'07-#@9016#@',
		'07-#@9017#@',
		'07-#@9018#@',
		'07-#@9019#@',
		'07-#@9020#@',
		'07-#@9021#@',
		'07-#@9022#@',
		'07-#@9023#@',
		'07-#@9024#@',
		'07-#@9025#@',
		'07-#@9026#@',
		'07-#@9027#@',
		'07-#@9028#@',
		'07-#@9029#@',
		'08',
		'08-#@8000#@',
		'08-#@8001#@',
		'08-#@8002#@',
		'08-#@8003#@',
		'08-#@8004#@',
		'08-#@8005#@',
		'08-#@8006#@',
		'08-#@8007#@',
		'08-#@8008#@',
		'08-#@8009#@',
		'09',
		'10',
		'99',
	])

	await db.createEnum('public.rents_status_enum', ['Ativo', 'Encerrado', 'Rescindido', 'Inativo'])

	await db.createEnum('public.states_abbreviation_enum', [
		'AC',
		'AL',
		'AP',
		'AM',
		'BA',
		'CE',
		'DF',
		'ES',
		'GO',
		'MA',
		'MT',
		'MS',
		'MG',
		'PA',
		'PB',
		'PR',
		'PE',
		'PI',
		'RJ',
		'RN',
		'RS',
		'RO',
		'RR',
		'SC',
		'SP',
		'SE',
		'TO',
		'EX',
	])

	await db.createEnum('public.states_label_enum', [
		'Acre',
		'Alagoas',
		'Amapá',
		'Amazonas',
		'Bahia',
		'Ceará',
		'Distrito Federal',
		'Espírito Santo',
		'Goiás',
		'Maranhão',
		'Mato Grosso',
		'Mato Grosso do Sul',
		'Minas Gerais',
		'Pará',
		'Paraíba',
		'Paraná',
		'Pernambuco',
		'Piauí',
		'Rio de Janeiro',
		'Rio Grande do Norte',
		'Rio Grande do Sul',
		'Rondônia',
		'Roraima',
		'Santa Catarina',
		'São Paulo',
		'Sergipe',
		'Tocantins',
		'Exterior',
	])

	await db.createEnum('public.tax_documents_types_enum', ['DARF', 'DAE', 'DAM'])

	await db.createEnum('public.then_that_enum', [
		'Adicione um alerta na nota fiscal',
		'Defina o PIS como cumulativo com aíquota de 0,65%',
		'Defina a COFINS como cumlativa com aíquota de 3%',
		'Defina o Lucro Presumido como 32%',
		'Defina o Lucro Presumido como 8%',
	])

	await db.createEnum('public.xml_configs_to_enum', [
		'accessKey',
		'date',
		'nsu',
		'type',
		'prestadorCpfCnpj',
		'prestadorNome',
		'tomadorCpfCnpj',
		'tomadorNome',
		'intermediarioCpfCnpj',
		'intermediarioNome',
		'value',
		'unconditionalDiscount',
		'conditionalDiscount',
		'iss',
		'issRetido',
		'pisRetido',
		'cofinsRetido',
		'irRetido',
		'csllRetido',
		'inssRetido',
		'description',
		'number',
		'simplesNacional',
		'specialRegim',
		'localServiceCode',
		'nacionalServiceCode',
		'cityCode',
		'tomadorIsPessoaJuridica',
		'otherDeductions',
		'erpKey',
		'pdfUrl',
		'origin',
	])

	await db.createTable(
		'keycloak.admin_event_entity',
		t => ({
			id: t.varchar(36).primaryKey(),
			adminEventTime: t.name('admin_event_time').bigint().nullable(),
			realmId: t.name('realm_id').varchar(255).nullable(),
			operationType: t.name('operation_type').varchar(255).nullable(),
			authRealmId: t.name('auth_realm_id').varchar(255).nullable(),
			authClientId: t.name('auth_client_id').varchar(255).nullable(),
			authUserId: t.name('auth_user_id').varchar(255).nullable(),
			ipAddress: t.name('ip_address').varchar(255).nullable(),
			resourcePath: t.name('resource_path').varchar(2550).nullable(),
			representation: t.text().nullable(),
			error: t.varchar(255).nullable(),
			resourceType: t.name('resource_type').varchar(64).nullable(),
			detailsJson: t.name('details_json').text().nullable(),
		}),
		t => t.index(['realmId', 'adminEventTime'], 'idx_admin_event_time'),
	)

	await db.createTable(
		'keycloak.authenticator_config_entry',
		t => ({
			authenticatorId: t.name('authenticator_id').varchar(36),
			value: t.text().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['authenticatorId', 'name'], 'constraint_auth_cfg_pk'),
	)

	await db.createTable('banking_banks', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		code: t.integer().nullable().unique(),
		ispb: t.type('bpchar').unique(),
		name: t.text(),
		fullName: t.name('full_name').text(),
	}))

	await db.createTable(
		'keycloak.broker_link',
		t => ({
			identityProvider: t.name('identity_provider').varchar(255),
			storageProviderId: t.name('storage_provider_id').varchar(255).nullable(),
			realmId: t.name('realm_id').varchar(36),
			brokerUserId: t.name('broker_user_id').varchar(255).nullable(),
			brokerUsername: t.name('broker_username').varchar(255).nullable(),
			token: t.text().nullable(),
			userId: t.name('user_id').varchar(255),
		}),
		t => t.primaryKey(['identityProvider', 'userId'], 'constr_broker_link_pk'),
	)

	await db.createTable('classifications', t => ({
		description: t.text(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		exampleFileName: t.name('example_file_name').text().nullable(),
		exampleFileBase64: t.name('example_file_base64').text().nullable(),
		antiExampleFileName: t.name('anti_example_file_name').text().nullable(),
		antiExampleFileBase64: t.name('anti_example_file_base64').text().nullable(),
	}))

	await db.createTable(
		'keycloak.client',
		t => ({
			id: t.varchar(36).primaryKey(),
			enabled: t.boolean().default(t.sql`false`),
			fullScopeAllowed: t.name('full_scope_allowed').boolean().default(t.sql`false`),
			clientId: t.name('client_id').varchar(255).nullable().index(),
			notBefore: t.name('not_before').integer().nullable(),
			publicClient: t.name('public_client').boolean().default(t.sql`false`),
			secret: t.varchar(255).nullable(),
			baseUrl: t.name('base_url').varchar(255).nullable(),
			bearerOnly: t.name('bearer_only').boolean().default(t.sql`false`),
			managementUrl: t.name('management_url').varchar(255).nullable(),
			surrogateAuthRequired: t.name('surrogate_auth_required').boolean().default(t.sql`false`),
			realmId: t.name('realm_id').varchar(36).nullable(),
			protocol: t.varchar(255).nullable(),
			nodeReregTimeout: t.name('node_rereg_timeout').integer().nullable().default(t.sql`0`),
			frontchannelLogout: t.name('frontchannel_logout').boolean().default(t.sql`false`),
			consentRequired: t.name('consent_required').boolean().default(t.sql`false`),
			name: t.varchar(255).nullable(),
			serviceAccountsEnabled: t.name('service_accounts_enabled').boolean().default(t.sql`false`),
			clientAuthenticatorType: t.name('client_authenticator_type').varchar(255).nullable(),
			rootUrl: t.name('root_url').varchar(255).nullable(),
			description: t.varchar(255).nullable(),
			registrationToken: t.name('registration_token').varchar(255).nullable(),
			standardFlowEnabled: t.name('standard_flow_enabled').boolean().default(t.sql`true`),
			implicitFlowEnabled: t.name('implicit_flow_enabled').boolean().default(t.sql`false`),
			directAccessGrantsEnabled: t.name('direct_access_grants_enabled').boolean().default(t.sql`false`),
			alwaysDisplayInConsole: t.name('always_display_in_console').boolean().default(t.sql`false`),
		}),
		t => t.unique(['realmId', 'clientId'], 'uk_b71cjlbenv945rb6gcon438at'),
	)

	await db.createTable(
		'keycloak.client_auth_flow_bindings',
		t => ({
			clientId: t.name('client_id').varchar(36),
			flowId: t.name('flow_id').varchar(36).nullable(),
			bindingName: t.name('binding_name').varchar(255),
		}),
		t => t.primaryKey(['clientId', 'bindingName'], 'c_cli_flow_bind'),
	)

	await db.createTable(
		'keycloak.client_scope',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255).nullable(),
			realmId: t.name('realm_id').varchar(36).nullable().index(),
			description: t.varchar(255).nullable(),
			protocol: t.varchar(255).nullable(),
		}),
		t => t.unique(['realmId', 'name'], 'uk_cli_scope'),
	)

	await db.createTable(
		'keycloak.client_scope_client',
		t => ({
			clientId: t.name('client_id').varchar(255).index(),
			scopeId: t.name('scope_id').varchar(255).index(),
			defaultScope: t.name('default_scope').boolean().default(t.sql`false`),
		}),
		t => t.primaryKey(['clientId', 'scopeId'], 'c_cli_scope_bind'),
	)

	await db.createTable(
		'custom_balance_sheet',
		{
			noPrimaryKey: true,
		},
		t => ({
			customerId: t.name('customer_id').uuid(),
			from: t.text(),
			to: t.text(),
			title: t.text(),
		}),
		t => t.unique(['customerId', 'from', 'to'], 'custom_balance_sheet_customer_id'),
	)

	await db.createTable(
		'customers_taxes_configs',
		t => ({
			customerId: t.name('customer_id').uuid(),
			from: t.timestampNoTZ(),
			until: t.timestampNoTZ(),
			irrfRent: t.name('irrf_rent').boolean().default(t.sql`false`),
			issqnSubstitute: t.name('issqn_substitute').boolean().default(t.sql`false`),
			dapi: t.boolean().default(t.sql`false`),
			ipiTaxPayer: t.name('ipi_tax_payer').boolean().default(t.sql`false`),
			cprb: t.boolean().default(t.sql`false`),
			icmsSpecialRegim: t.name('icms_special_regim').boolean().default(t.sql`false`),
			unifiedDctfwebTaxes: t.name('unified_dctfweb_taxes').boolean().default(t.sql`false`),
			hasActivities: t.name('has_activities').boolean().default(t.sql`false`),
		}),
		t => t.primaryKey(['customerId', 'from', 'until'], 'customers_taxes_configs_customer_id_from_until_pk'),
	)

	await db.createTable(
		'keycloak.databasechangelog',
		{
			noPrimaryKey: true,
		},
		t => ({
			id: t.varchar(255),
			author: t.varchar(255),
			filename: t.varchar(255),
			dateexecuted: t.timestampNoTZ(),
			orderexecuted: t.integer(),
			exectype: t.varchar(10),
			md5sum: t.varchar(35).nullable(),
			description: t.varchar(255).nullable(),
			comments: t.varchar(255).nullable(),
			tag: t.varchar(255).nullable(),
			liquibase: t.varchar(20).nullable(),
			contexts: t.varchar(255).nullable(),
			labels: t.varchar(255).nullable(),
			deploymentId: t.name('deployment_id').varchar(10).nullable(),
		}),
	)

	await db.createTable('keycloak.databasechangeloglock', t => ({
		id: t.integer().primaryKey(),
		locked: t.boolean(),
		lockgranted: t.timestampNoTZ().nullable(),
		lockedby: t.varchar(255).nullable(),
	}))

	await db.createTable(
		'keycloak.event_entity',
		t => ({
			id: t.varchar(36).primaryKey(),
			clientId: t.name('client_id').varchar(255).nullable(),
			detailsJson: t.name('details_json').varchar(2550).nullable(),
			error: t.varchar(255).nullable(),
			ipAddress: t.name('ip_address').varchar(255).nullable(),
			realmId: t.name('realm_id').varchar(255).nullable(),
			sessionId: t.name('session_id').varchar(255).nullable(),
			eventTime: t.name('event_time').bigint().nullable(),
			type: t.varchar(255).nullable(),
			userId: t.name('user_id').varchar(255).nullable(),
			detailsJsonLongValue: t.name('details_json_long_value').text().nullable(),
		}),
		t => t.index(['realmId', 'eventTime'], 'idx_event_time'),
	)

	await db.createTable(
		'keycloak.fed_user_attribute',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
			value: t.varchar(2024).nullable(),
			longValueHash: t.name('long_value_hash').bytea().nullable(),
			longValueHashLowerCase: t.name('long_value_hash_lower_case').bytea().nullable(),
			longValue: t.name('long_value').text().nullable(),
		}),
		t => [
			t.index(['longValueHash', 'name'], 'fed_user_attr_long_values'),
			t.index(['longValueHashLowerCase', 'name'], 'fed_user_attr_long_values_lower_case'),
			t.index(['userId', 'realmId', 'name'], 'idx_fu_attribute'),
		],
	)

	await db.createTable(
		'keycloak.fed_user_consent',
		t => ({
			id: t.varchar(36).primaryKey(),
			clientId: t.name('client_id').varchar(255).nullable(),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
			createdDate: t.name('created_date').bigint().nullable(),
			lastUpdatedDate: t.name('last_updated_date').bigint().nullable(),
			clientStorageProvider: t.name('client_storage_provider').varchar(36).nullable(),
			externalClientId: t.name('external_client_id').varchar(255).nullable(),
		}),
		t => [
			t.index(['userId', 'clientStorageProvider', 'externalClientId'], 'idx_fu_cnsnt_ext'),
			t.index(['userId', 'clientId'], 'idx_fu_consent'),
			t.index(['realmId', 'userId'], 'idx_fu_consent_ru'),
		],
	)

	await db.createTable(
		'keycloak.fed_user_consent_cl_scope',
		t => ({
			userConsentId: t.name('user_consent_id').varchar(36),
			scopeId: t.name('scope_id').varchar(36),
		}),
		t => t.primaryKey(['userConsentId', 'scopeId'], 'constraint_fgrntcsnt_clsc_pm'),
	)

	await db.createTable(
		'keycloak.fed_user_credential',
		t => ({
			id: t.varchar(36).primaryKey(),
			salt: t.bytea().nullable(),
			type: t.varchar(255).nullable(),
			createdDate: t.name('created_date').bigint().nullable(),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
			userLabel: t.name('user_label').varchar(255).nullable(),
			secretData: t.name('secret_data').text().nullable(),
			credentialData: t.name('credential_data').text().nullable(),
			priority: t.integer().nullable(),
		}),
		t => [t.index(['userId', 'type'], 'idx_fu_credential'), t.index(['realmId', 'userId'], 'idx_fu_credential_ru')],
	)

	await db.createTable(
		'keycloak.fed_user_group_membership',
		t => ({
			groupId: t.name('group_id').varchar(36),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
		}),
		t => [
			t.primaryKey(['groupId', 'userId'], 'constr_fed_user_group'),
			t.index(['userId', 'groupId'], 'idx_fu_group_membership'),
			t.index(['realmId', 'userId'], 'idx_fu_group_membership_ru'),
		],
	)

	await db.createTable(
		'keycloak.fed_user_required_action',
		t => ({
			requiredAction: t.name('required_action').varchar(255).default(t.sql`' '::character varying`),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
		}),
		t => [
			t.primaryKey(['requiredAction', 'userId'], 'constr_fed_required_action'),
			t.index(['userId', 'requiredAction'], 'idx_fu_required_action'),
			t.index(['realmId', 'userId'], 'idx_fu_required_action_ru'),
		],
	)

	await db.createTable(
		'keycloak.fed_user_role_mapping',
		t => ({
			roleId: t.name('role_id').varchar(36),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			storageProviderId: t.name('storage_provider_id').varchar(36).nullable(),
		}),
		t => [
			t.primaryKey(['roleId', 'userId'], 'constr_fed_user_role'),
			t.index(['userId', 'roleId'], 'idx_fu_role_mapping'),
			t.index(['realmId', 'userId'], 'idx_fu_role_mapping_ru'),
		],
	)

	await db.createTable('keycloak.federated_user', t => ({
		id: t.varchar(255).primaryKey(),
		storageProviderId: t.name('storage_provider_id').varchar(255).nullable(),
		realmId: t.name('realm_id').varchar(36),
	}))

	await db.createTable(
		'file_packs',
		{
			noPrimaryKey: true,
		},
		t => ({
			created: t.timestampNoTZ().default(t.sql`now()`),
			id: t.uuid().default(t.sql`gen_random_uuid()`),
		}),
	)

	await db.createTable('help', t => ({
		author: t.text(),
		authorAvatar: t.name('author_avatar').text(),
		authorRole: t.name('author_role').text(),
		date: t.timestampNoTZ(),
		description: t.text(),
		folder: t.text(),
		html: t.text(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		roles: t.json(),
		title: t.text(),
	}))

	await db.createTable('ie', t => ({
		aberturaData: t.name('abertura_data').text(),
		atividadeEconomica: t.name('atividade_economica').text(),
		atividadeEconomicaSecundaria: t.name('atividade_economica_secundaria').text(),
		cnpj: t.text(),
		cnpjCpf: t.name('cnpj_cpf').text(),
		consultaData: t.name('consulta_data').text(),
		consultaDatahora: t.name('consulta_datahora').text(),
		cpf: t.text(),
		email: t.text(),
		enderecoBairro: t.name('endereco_bairro').text(),
		enderecoCep: t.name('endereco_cep').text(),
		enderecoComplemento: t.name('endereco_complemento').text(),
		enderecoLogradouro: t.name('endereco_logradouro').text(),
		enderecoMunicipio: t.name('endereco_municipio').text(),
		enderecoNumero: t.name('endereco_numero').text(),
		enderecoUf: t.name('endereco_uf').text(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		inscricaoEstadual: t.name('inscricao_estadual').text(),
		nomeFantasia: t.name('nome_fantasia').text(),
		normalizadoAberturaData: t.name('normalizado_abertura_data').text(),
		normalizadoCnpj: t.name('normalizado_cnpj').text().unique(),
		normalizadoCnpjCpf: t.name('normalizado_cnpj_cpf').text(),
		normalizadoConsultaData: t.name('normalizado_consulta_data').text(),
		normalizadoConsultaDatahora: t.name('normalizado_consulta_datahora').text(),
		normalizadoCpf: t.name('normalizado_cpf').text(),
		normalizadoEnderecoCep: t.name('normalizado_endereco_cep').text(),
		normalizadoInscricaoEstadual: t.name('normalizado_inscricao_estadual').text(),
		normalizadoSituacaoCadastralData: t.name('normalizado_situacao_cadastral_data').text(),
		razaoSocial: t.name('razao_social').text(),
		regimeApuracao: t.name('regime_apuracao').text(),
		siteReceipt: t.name('site_receipt').text(),
		siteReceiptBase64: t.name('site_receipt_base64').text().nullable(),
		situacaoCadastral: t.name('situacao_cadastral').text(),
		situacaoCadastralData: t.name('situacao_cadastral_data').text(),
		telefone: t.text(),
	}))

	await db.createTable(
		'invoices',
		t => ({
			payerTaxNumber: t.name('payer_tax_number').varchar(),
			referenceDate: t.name('reference_date').timestampNoTZ(),
			installmentNumber: t.name('installment_number').integer(),
			accessKey: t.name('access_key').varchar(),
			number: t.varchar(),
		}),
		t =>
			t.primaryKey(
				['payerTaxNumber', 'referenceDate', 'installmentNumber'],
				'invoices_payer_tax_number_reference_date_installment_number_pk',
			),
	)

	await db.createTable('justificativas_tipos', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		titulo: t.varchar(),
		descricao: t.text(),
	}))

	await db.createTable(
		'keycloak.keycloak_group',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255).nullable(),
			parentGroup: t.name('parent_group').varchar(36),
			realmId: t.name('realm_id').varchar(36).nullable(),
			type: t.integer().default(t.sql`0`),
		}),
		t => t.unique(['realmId', 'parentGroup', 'name'], 'sibling_names'),
	)

	await db.createTable('keycloak.migration_model', t => ({
		id: t.varchar(36).primaryKey(),
		version: t.varchar(36).nullable(),
		updateTime: t.name('update_time').bigint().default(t.sql`0`).index(),
	}))

	await db.createTable('ncm', t => ({
		ano: t.text().nullable(),
		codigo: t.text().primaryKey(),
		dataFim: t.name('data_fim').text(),
		dataInicio: t.name('data_inicio').text(),
		descricao: t.text(),
		numeroAto: t.name('numero_ato').text(),
		tipoAto: t.name('tipo_ato').text(),
	}))

	await db.createTable(
		'keycloak.offline_client_session',
		t => ({
			userSessionId: t.name('user_session_id').varchar(36),
			clientId: t.name('client_id').varchar(255),
			offlineFlag: t.name('offline_flag').varchar(4),
			timestamp: t.integer().nullable(),
			data: t.text().nullable(),
			clientStorageProvider: t.name('client_storage_provider').varchar(36).default(t.sql`'local'::character varying`),
			externalClientId: t.name('external_client_id').varchar(255).default(t.sql`'local'::character varying`),
			version: t.integer().nullable().default(t.sql`0`),
		}),
		t =>
			t.primaryKey(['userSessionId', 'clientId', 'offlineFlag', 'clientStorageProvider', 'externalClientId'], 'constraint_offl_cl_ses_pk3'),
	)

	await db.createTable(
		'keycloak.offline_user_session',
		t => ({
			userSessionId: t.name('user_session_id').varchar(36),
			userId: t.name('user_id').varchar(255),
			realmId: t.name('realm_id').varchar(36),
			createdOn: t.name('created_on').integer(),
			offlineFlag: t.name('offline_flag').varchar(4),
			data: t.text().nullable(),
			lastSessionRefresh: t.name('last_session_refresh').integer().default(t.sql`0`),
			brokerSessionId: t.name('broker_session_id').varchar(1024).nullable(),
			version: t.integer().nullable().default(t.sql`0`),
		}),
		t => [
			t.primaryKey(['userSessionId', 'offlineFlag'], 'constraint_offl_us_ses_pk2'),
			t.index(['brokerSessionId', 'realmId'], 'idx_offline_uss_by_broker_session_id'),
			t.index(['realmId', 'offlineFlag', 'lastSessionRefresh'], 'idx_offline_uss_by_last_session_refresh'),
			t.index(['userId', 'realmId', 'offlineFlag'], 'idx_offline_uss_by_user'),
		],
	)

	await db.createTable(
		'keycloak.org',
		t => ({
			id: t.varchar(255).primaryKey(),
			enabled: t.boolean(),
			realmId: t.name('realm_id').varchar(255),
			groupId: t.name('group_id').varchar(255).unique(),
			name: t.varchar(255),
			description: t.varchar(4000).nullable(),
			alias: t.varchar(255),
			redirectUrl: t.name('redirect_url').varchar(2048).nullable(),
		}),
		t => [t.unique(['realmId', 'alias'], 'uk_org_alias'), t.unique(['realmId', 'name'], 'uk_org_name')],
	)

	await db.createTable(
		'keycloak.org_domain',
		t => ({
			id: t.varchar(36),
			name: t.varchar(255),
			verified: t.boolean(),
			orgId: t.name('org_id').varchar(255).index(),
		}),
		t => t.primaryKey(['id', 'name'], 'ORG_DOMAIN_pkey'),
	)

	await db.createTable('payroll_importer', t => ({
		customerId: t.uuid().primaryKey(),
		events: t.json(),
	}))

	await db.createTable('keycloak.realm', t => ({
		id: t.varchar(36).primaryKey(),
		accessCodeLifespan: t.name('access_code_lifespan').integer().nullable(),
		userActionLifespan: t.name('user_action_lifespan').integer().nullable(),
		accessTokenLifespan: t.name('access_token_lifespan').integer().nullable(),
		accountTheme: t.name('account_theme').varchar(255).nullable(),
		adminTheme: t.name('admin_theme').varchar(255).nullable(),
		emailTheme: t.name('email_theme').varchar(255).nullable(),
		enabled: t.boolean().default(t.sql`false`),
		eventsEnabled: t.name('events_enabled').boolean().default(t.sql`false`),
		eventsExpiration: t.name('events_expiration').bigint().nullable(),
		loginTheme: t.name('login_theme').varchar(255).nullable(),
		name: t.varchar(255).nullable().unique(),
		notBefore: t.name('not_before').integer().nullable(),
		passwordPolicy: t.name('password_policy').varchar(2550).nullable(),
		registrationAllowed: t.name('registration_allowed').boolean().default(t.sql`false`),
		rememberMe: t.name('remember_me').boolean().default(t.sql`false`),
		resetPasswordAllowed: t.name('reset_password_allowed').boolean().default(t.sql`false`),
		social: t.boolean().default(t.sql`false`),
		sslRequired: t.name('ssl_required').varchar(255).nullable(),
		ssoIdleTimeout: t.name('sso_idle_timeout').integer().nullable(),
		ssoMaxLifespan: t.name('sso_max_lifespan').integer().nullable(),
		updateProfileOnSocLogin: t.name('update_profile_on_soc_login').boolean().default(t.sql`false`),
		verifyEmail: t.name('verify_email').boolean().default(t.sql`false`),
		masterAdminClient: t.name('master_admin_client').varchar(36).nullable().index(),
		loginLifespan: t.name('login_lifespan').integer().nullable(),
		internationalizationEnabled: t.name('internationalization_enabled').boolean().default(t.sql`false`),
		defaultLocale: t.name('default_locale').varchar(255).nullable(),
		regEmailAsUsername: t.name('reg_email_as_username').boolean().default(t.sql`false`),
		adminEventsEnabled: t.name('admin_events_enabled').boolean().default(t.sql`false`),
		adminEventsDetailsEnabled: t.name('admin_events_details_enabled').boolean().default(t.sql`false`),
		editUsernameAllowed: t.name('edit_username_allowed').boolean().default(t.sql`false`),
		otpPolicyCounter: t.name('otp_policy_counter').integer().nullable().default(t.sql`0`),
		otpPolicyWindow: t.name('otp_policy_window').integer().nullable().default(t.sql`1`),
		otpPolicyPeriod: t.name('otp_policy_period').integer().nullable().default(t.sql`30`),
		otpPolicyDigits: t.name('otp_policy_digits').integer().nullable().default(t.sql`6`),
		otpPolicyAlg: t.name('otp_policy_alg').varchar(36).nullable().default(t.sql`'HmacSHA1'::character varying`),
		otpPolicyType: t.name('otp_policy_type').varchar(36).nullable().default(t.sql`'totp'::character varying`),
		browserFlow: t.name('browser_flow').varchar(36).nullable(),
		registrationFlow: t.name('registration_flow').varchar(36).nullable(),
		directGrantFlow: t.name('direct_grant_flow').varchar(36).nullable(),
		resetCredentialsFlow: t.name('reset_credentials_flow').varchar(36).nullable(),
		clientAuthFlow: t.name('client_auth_flow').varchar(36).nullable(),
		offlineSessionIdleTimeout: t.name('offline_session_idle_timeout').integer().nullable().default(t.sql`0`),
		revokeRefreshToken: t.name('revoke_refresh_token').boolean().default(t.sql`false`),
		accessTokenLifeImplicit: t.name('access_token_life_implicit').integer().nullable().default(t.sql`0`),
		loginWithEmailAllowed: t.name('login_with_email_allowed').boolean().default(t.sql`true`),
		duplicateEmailsAllowed: t.name('duplicate_emails_allowed').boolean().default(t.sql`false`),
		dockerAuthFlow: t.name('docker_auth_flow').varchar(36).nullable(),
		refreshTokenMaxReuse: t.name('refresh_token_max_reuse').integer().nullable().default(t.sql`0`),
		allowUserManagedAccess: t.name('allow_user_managed_access').boolean().default(t.sql`false`),
		ssoMaxLifespanRememberMe: t.name('sso_max_lifespan_remember_me').integer().default(t.sql`0`),
		ssoIdleTimeoutRememberMe: t.name('sso_idle_timeout_remember_me').integer().default(t.sql`0`),
		defaultRole: t.name('default_role').varchar(255).nullable(),
	}))

	await db.createTable(
		'keycloak.realm_localizations',
		t => ({
			realmId: t.name('realm_id').varchar(255),
			locale: t.varchar(255),
			texts: t.text(),
		}),
		t => t.primaryKey(['realmId', 'locale']),
	)

	await db.createTable(
		'keycloak.required_action_config',
		t => ({
			requiredActionId: t.name('required_action_id').varchar(36),
			value: t.text().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['requiredActionId', 'name'], 'constraint_req_act_cfg_pk'),
	)

	await db.createTable('keycloak.resource_server', t => ({
		id: t.varchar(36).primaryKey(),
		allowRsRemoteMgmt: t.name('allow_rs_remote_mgmt').boolean().default(t.sql`false`),
		policyEnforceMode: t.name('policy_enforce_mode').smallint(),
		decisionStrategy: t.name('decision_strategy').smallint().default(t.sql`1`),
	}))

	await db.createTable('keycloak.revoked_token', t => ({
		id: t.varchar(255).primaryKey(),
		expire: t.bigint().index(),
	}))

	await db.createTable('roles', t => ({
		id: t.uuid().primaryKey(),
		title: t.text(),
	}))

	await db.createTable('tags', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text(),
	}))

	await db.createTable('tasks', t => ({
		dateEnd: t.name('date_end').timestampNoTZ().nullable(),
		dateStart: t.name('date_start').timestampNoTZ(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text(),
		applyToRules: t.name('apply_to_rules').json().default(t.sql`'{}'::jsonb`),
	}))

	await db.createTable('templates', t => ({
		groups: t.json().default(t.sql`'[]'::jsonb`),
		html: t.text(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		json: t.json(),
		parameters: t.json(),
		title: t.varchar(),
	}))

	await db.createTable(
		'keycloak.user_entity',
		t => ({
			id: t.varchar(36).primaryKey(),
			email: t.varchar(255).nullable().index(),
			emailConstraint: t.name('email_constraint').varchar(255).nullable(),
			emailVerified: t.name('email_verified').boolean().default(t.sql`false`),
			enabled: t.boolean().default(t.sql`false`),
			federationLink: t.name('federation_link').varchar(255).nullable(),
			firstName: t.name('first_name').varchar(255).nullable(),
			lastName: t.name('last_name').varchar(255).nullable(),
			realmId: t.name('realm_id').varchar(255).nullable(),
			username: t.varchar(255).nullable(),
			createdTimestamp: t.name('created_timestamp').bigint().nullable(),
			serviceAccountClientLink: t.name('service_account_client_link').varchar(255).nullable(),
			notBefore: t.name('not_before').integer().default(t.sql`0`),
		}),
		t => [
			t.index(['realmId', 'serviceAccountClientLink'], 'idx_user_service_account'),
			t.unique(['realmId', 'emailConstraint'], 'uk_dykn684sl8up1crfei6eckhd7'),
			t.unique(['realmId', 'username'], 'uk_ru8tt6t700s9v50bu18ws5ha6'),
		],
	)

	await db.createTable(
		'keycloak.username_login_failure',
		t => ({
			realmId: t.name('realm_id').varchar(36),
			username: t.varchar(255),
			failedLoginNotBefore: t.name('failed_login_not_before').integer().nullable(),
			lastFailure: t.name('last_failure').bigint().nullable(),
			lastIpFailure: t.name('last_ip_failure').varchar(255).nullable(),
			numFailures: t.name('num_failures').integer().nullable(),
		}),
		t => t.primaryKey(['realmId', 'username'], 'CONSTRAINT_17-2'),
	)

	await db.createTable(
		'whatsapp',
		t => ({
			mattermostId: t.text(),
			whatsAppId: t.text(),
		}),
		t => t.primaryKey(['mattermostId', 'whatsAppId'], 'whatsapp_whatsappid_mattermostid_pk'),
	)

	await db.createTable('xml_types', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text().unique(),
		xmlSample: t.name('xml_sample').text(),
		uniqueIdentifier: t.name('unique_identifier').text().unique(),
	}))
})

change(async db => {
	await db.createTable('keycloak.authentication_flow', t => ({
		id: t.varchar(36).primaryKey(),
		alias: t.varchar(255).nullable(),
		description: t.varchar(255).nullable(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_auth_flow_realm',
			})
			.nullable()
			.index(),
		providerId: t.name('provider_id').varchar(36).default(t.sql`'basic-flow'::character varying`),
		topLevel: t.name('top_level').boolean().default(t.sql`false`),
		builtIn: t.name('built_in').boolean().default(t.sql`false`),
	}))

	await db.createTable('keycloak.authenticator_config', t => ({
		id: t.varchar(36).primaryKey(),
		alias: t.varchar(255).nullable(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_auth_realm',
			})
			.nullable()
			.index(),
	}))

	await db.createTable('banking_pix', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		customerId: t.name('customer_id').uuid(),
		bankId: t.name('bank_id').uuid(),
		accountType: t.name('account_type').enum('public.banking_pix_account_types_enum'),
		agencyNumber: t.name('agency_number').type('bpchar'),
		accountNumber: t.name('account_number').varchar(),
		receiverName: t.name('receiver_name').text(),
		receiverTaxNumber: t.name('receiver_tax_number').varchar(),
		description: t.text(),
		pixType: t.name('pix_type').enum('public.banking_pix_types_enum'),
		status: t.enum('public.banking_pix_status_enum'),
		value: t.decimal(),
		created: t.timestampNoTZ().default(t.sql`now()`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
	}))

	await db.createTable('classifications_flows_tasks', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		classificationId: t.name('classification_id').uuid().foreignKey('classifications', 'id', {
			name: 'classifications_flows_tasks_classification_id_classifications_i',
			onDelete: 'CASCADE',
		}),
		competenciaFormat: t.name('competencia_format').enum('public.classifications_flows_tasks_date_format'),
		competenciaIndex: t.name('competencia_index').integer(),
		identifier: t.enum('public.classifications_flows_tasks_identifier'),
		identifierIndex: t.name('identifier_index').integer(),
		identifierRegex: t.name('identifier_regex').text().nullable(),
	}))

	await db.createTable('classifications_flows_taxes', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		classificationId: t.name('classification_id').uuid().foreignKey('classifications', 'id', {
			name: 'classifications_flows_taxes_classification_id_classifications_i',
			onDelete: 'CASCADE',
		}),
		competenciaFormat: t.name('competencia_format').enum('public.classifications_flows_taxes_date_format'),
		competenciaIndex: t.name('competencia_index').integer(),
		identifier: t.enum('public.classifications_flows_taxes_identifier'),
		identifierIndex: t.name('identifier_index').integer(),
		tax: t.enum('public.classifications_flows_taxes_guia'),
		valorIndex: t.name('valor_index').integer(),
		vencimentoFormat: t.name('vencimento_format').enum('public.classifications_flows_taxes_date_format'),
		vencimentoIndex: t.name('vencimento_index').integer(),
	}))

	await db.createTable('classifications_rules', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		classificationId: t.name('classification_id').uuid().foreignKey('classifications', 'id', {
			name: 'classifications_rules_classification_id_classifications_id_fk',
			onDelete: 'CASCADE',
		}),
		column: t.integer(),
		row: t.integer(),
		value: t.text(),
		ruleType: t.name('rule_type').enum('public.classification_rules_type'),
	}))

	await db.createTable(
		'keycloak.client_attributes',
		t => ({
			clientId: t.name('client_id').varchar(36).foreignKey('keycloak.client', 'id', {
				name: 'fk3c47c64beacca966',
			}),
			name: t.varchar(255),
			value: t.text().nullable(),
		}),
		t => [
			t.primaryKey(['clientId', 'name'], 'constraint_3c'),
			t.index(
				[
					'name',
					{
						expression: 'substr(value, 1, 255)',
					},
				],
				'idx_client_att_by_name_value',
			),
		],
	)

	await db.createTable('keycloak.client_initial_access', t => ({
		id: t.varchar(36).primaryKey(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_client_init_acc_realm',
			})
			.index(),
		timestamp: t.integer().nullable(),
		expiration: t.integer().nullable(),
		count: t.integer().nullable(),
		remainingCount: t.name('remaining_count').integer().nullable(),
	}))

	await db.createTable(
		'keycloak.client_node_registrations',
		t => ({
			clientId: t.name('client_id').varchar(36).foreignKey('keycloak.client', 'id', {
				name: 'fk4129723ba992f594',
			}),
			value: t.integer().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['clientId', 'name'], 'constraint_84'),
	)

	await db.createTable(
		'keycloak.client_scope_attributes',
		t => ({
			scopeId: t
				.name('scope_id')
				.varchar(36)
				.foreignKey('keycloak.client_scope', 'id', {
					name: 'fk_cl_scope_attr_scope',
				})
				.index(),
			value: t.varchar(2048).nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['scopeId', 'name'], 'pk_cl_tmpl_attr'),
	)

	await db.createTable(
		'keycloak.client_scope_role_mapping',
		t => ({
			scopeId: t
				.name('scope_id')
				.varchar(36)
				.foreignKey('keycloak.client_scope', 'id', {
					name: 'fk_cl_scope_rm_scope',
				})
				.index(),
			roleId: t.name('role_id').varchar(36).index(),
		}),
		t => t.primaryKey(['scopeId', 'roleId'], 'pk_template_scope'),
	)

	await db.createTable('keycloak.component', t => ({
		id: t.varchar(36).primaryKey(),
		name: t.varchar(255).nullable(),
		parentId: t.name('parent_id').varchar(36).nullable(),
		providerId: t.name('provider_id').varchar(36).nullable(),
		providerType: t.name('provider_type').varchar(255).nullable().index(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_component_realm',
			})
			.nullable()
			.index(),
		subType: t.name('sub_type').varchar(255).nullable(),
	}))

	await db.createTable('keycloak.credential', t => ({
		id: t.varchar(36).primaryKey(),
		salt: t.bytea().nullable(),
		type: t.varchar(255).nullable(),
		userId: t
			.name('user_id')
			.varchar(36)
			.foreignKey('keycloak.user_entity', 'id', {
				name: 'fk_pfyr0glasqyl0dei3kl69r6v0',
			})
			.nullable()
			.index(),
		createdDate: t.name('created_date').bigint().nullable(),
		userLabel: t.name('user_label').varchar(255).nullable(),
		secretData: t.name('secret_data').text().nullable(),
		credentialData: t.name('credential_data').text().nullable(),
		priority: t.integer().nullable(),
	}))

	await db.createTable(
		'customers_tax_timeline',
		t => ({
			customerId: t.name('customer_id').uuid(),
			from: t.timestampNoTZ(),
			until: t.timestampNoTZ(),
			tax: t.enum('public.tax_timeline_tax_enum'),
		}),
		t => t.primaryKey(['customerId', 'from', 'until'], 'customers_tax_timeline_customer_id_from_until_pk'),
	)

	await db.createTable('darf_codes', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		code: t.type('bpchar').index().unique(),
		description: t.text(),
		validityStart: t.name('validity_start').timestampNoTZ(),
		validityEnd: t.name('validity_end').timestampNoTZ().nullable(),
		pAType: t.name('p_a_type').enum('public.tax_documents_pa_enum'),
		group: t.enum('public.darf_codes_group_enum'),
	}))

	await db.createTable(
		'keycloak.default_client_scope',
		t => ({
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_r_def_cli_scope_realm',
				})
				.index(),
			scopeId: t.name('scope_id').varchar(36).index(),
			defaultScope: t.name('default_scope').boolean().default(t.sql`false`),
		}),
		t => t.primaryKey(['realmId', 'scopeId'], 'r_def_cli_scope_bind'),
	)

	await db.createTable('documents', t => ({
		applyToRules: t.name('apply_to_rules').json().default(t.sql`'{}'::jsonb`),
		dateEnd: t.name('date_end').timestampNoTZ().nullable(),
		dateStart: t.name('date_start').timestampNoTZ(),
		frequency: t.enum('public.documents_frequencies_enum'),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		path: t.text(),
		type: t.enum('public.documents_document_types_enum'),
		help: t.text().nullable(),
		title: t.text(),
	}))

	await db.createTable(
		'keycloak.federated_identity',
		t => ({
			identityProvider: t.name('identity_provider').varchar(255),
			realmId: t.name('realm_id').varchar(36).nullable(),
			federatedUserId: t.name('federated_user_id').varchar(255).nullable().index(),
			federatedUsername: t.name('federated_username').varchar(255).nullable(),
			token: t.text().nullable(),
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk404288b92ef007a6',
				})
				.index(),
		}),
		t => t.primaryKey(['identityProvider', 'userId'], 'constraint_40'),
	)

	await db.createTable(
		'keycloak.group_attribute',
		t => ({
			id: t.varchar(36).primaryKey().default(t.sql`'sybase-needs-something-here'::character varying`),
			name: t.varchar(255),
			value: t.varchar(255).nullable(),
			groupId: t
				.name('group_id')
				.varchar(36)
				.foreignKey('keycloak.keycloak_group', 'id', {
					name: 'fk_group_attribute_group',
				})
				.index(),
		}),
		t =>
			t.index(
				[
					'name',
					{
						expression: '((value)::character varying(250))',
					},
				],
				'idx_group_att_by_name_value',
			),
	)

	await db.createTable(
		'keycloak.group_role_mapping',
		t => ({
			roleId: t.name('role_id').varchar(36),
			groupId: t
				.name('group_id')
				.varchar(36)
				.foreignKey('keycloak.keycloak_group', 'id', {
					name: 'fk_group_role_group',
				})
				.index(),
		}),
		t => t.primaryKey(['roleId', 'groupId'], 'constraint_group_role'),
	)

	await db.createTable(
		'keycloak.identity_provider',
		t => ({
			internalId: t.name('internal_id').varchar(36).primaryKey(),
			enabled: t.boolean().default(t.sql`false`),
			providerAlias: t.name('provider_alias').varchar(255).nullable(),
			providerId: t.name('provider_id').varchar(255).nullable(),
			storeToken: t.name('store_token').boolean().default(t.sql`false`),
			authenticateByDefault: t.name('authenticate_by_default').boolean().default(t.sql`false`),
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk2b4ebc52ae5c3b34',
				})
				.nullable()
				.index(),
			addTokenRole: t.name('add_token_role').boolean().default(t.sql`true`),
			trustEmail: t.name('trust_email').boolean().default(t.sql`false`),
			firstBrokerLoginFlowId: t.name('first_broker_login_flow_id').varchar(36).nullable(),
			postBrokerLoginFlowId: t.name('post_broker_login_flow_id').varchar(36).nullable(),
			providerDisplayName: t.name('provider_display_name').varchar(255).nullable(),
			linkOnly: t.name('link_only').boolean().default(t.sql`false`),
			organizationId: t.name('organization_id').varchar(255).nullable(),
			hideOnLogin: t.name('hide_on_login').boolean().nullable().default(t.sql`false`),
		}),
		t => [
			t.index(['realmId', 'enabled', 'linkOnly', 'hideOnLogin', 'organizationId'], 'idx_idp_for_login'),
			t.index(['realmId', 'organizationId'], 'idx_idp_realm_org'),
			t.unique(['providerAlias', 'realmId'], 'uk_2daelwnibji49avxsrtuf6xj33'),
		],
	)

	await db.createTable('keycloak.identity_provider_mapper', t => ({
		id: t.varchar(36).primaryKey(),
		name: t.varchar(255),
		idpAlias: t.name('idp_alias').varchar(255),
		idpMapperName: t.name('idp_mapper_name').varchar(255),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_idpm_realm',
			})
			.index(),
	}))

	await db.createTable(
		'keycloak.keycloak_role',
		t => ({
			id: t.varchar(36).primaryKey(),
			clientRealmConstraint: t.name('client_realm_constraint').varchar(255).nullable(),
			clientRole: t.name('client_role').boolean().default(t.sql`false`),
			description: t.varchar(255).nullable(),
			name: t.varchar(255).nullable(),
			realmId: t.name('realm_id').varchar(255).nullable(),
			client: t.varchar(36).nullable().index(),
			realm: t
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_6vyqfe4cn4wlq8r6kt5vdsj5c',
				})
				.nullable()
				.index(),
		}),
		t => t.unique(['name', 'clientRealmConstraint'], 'UK_J3RWUVD56ONTGSUHOGM184WW2-2'),
	)

	await db.createTable('perfis', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		nome: t.varchar(),
		administracao: t.enum('public.permissao'),
		gestao: t.enum('public.permissao'),
		ponto: t.enum('public.permissao'),
	}))

	await db.createTable('keycloak.protocol_mapper', t => ({
		id: t.varchar(36).primaryKey(),
		name: t.varchar(255),
		protocol: t.varchar(255),
		protocolMapperName: t.name('protocol_mapper_name').varchar(255),
		clientId: t
			.name('client_id')
			.varchar(36)
			.foreignKey('keycloak.client', 'id', {
				name: 'fk_pcm_realm',
			})
			.nullable()
			.index(),
		clientScopeId: t
			.name('client_scope_id')
			.varchar(36)
			.foreignKey('keycloak.client_scope', 'id', {
				name: 'fk_cli_scope_mapper',
			})
			.nullable()
			.index(),
	}))

	await db.createTable(
		'keycloak.realm_attribute',
		t => ({
			name: t.varchar(255),
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_8shxd6l3e9atqukacxgpffptw',
				})
				.index(),
			value: t.text().nullable(),
		}),
		t => t.primaryKey(['name', 'realmId'], 'constraint_9'),
	)

	await db.createTable(
		'keycloak.realm_default_groups',
		t => ({
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_def_groups_realm',
				})
				.index(),
			groupId: t.name('group_id').varchar(36).unique(),
		}),
		t => t.primaryKey(['realmId', 'groupId'], 'constr_realm_default_groups'),
	)

	await db.createTable(
		'keycloak.realm_enabled_event_types',
		t => ({
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_h846o4h0w8epx5nwedrf5y69j',
				})
				.index(),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['realmId', 'value'], 'constr_realm_enabl_event_types'),
	)

	await db.createTable(
		'keycloak.realm_events_listeners',
		t => ({
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_h846o4h0w8epx5nxev9f5y69j',
				})
				.index(),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['realmId', 'value'], 'constr_realm_events_listeners'),
	)

	await db.createTable(
		'keycloak.realm_required_credential',
		t => ({
			type: t.varchar(255),
			formLabel: t.name('form_label').varchar(255).nullable(),
			input: t.boolean().default(t.sql`false`),
			secret: t.boolean().default(t.sql`false`),
			realmId: t.name('realm_id').varchar(36).foreignKey('keycloak.realm', 'id', {
				name: 'fk_5hg65lybevavkqfki3kponh9v',
			}),
		}),
		t => t.primaryKey(['type', 'realmId'], 'constraint_92'),
	)

	await db.createTable(
		'keycloak.realm_smtp_config',
		t => ({
			realmId: t.name('realm_id').varchar(36).foreignKey('keycloak.realm', 'id', {
				name: 'fk_70ej8xdxgxd0b9hh6180irr0o',
			}),
			value: t.varchar(255).nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['realmId', 'name'], 'constraint_e'),
	)

	await db.createTable(
		'keycloak.realm_supported_locales',
		t => ({
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_supported_locales_realm',
				})
				.index(),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['realmId', 'value'], 'constr_realm_supported_locales'),
	)

	await db.createTable(
		'keycloak.redirect_uris',
		t => ({
			clientId: t
				.name('client_id')
				.varchar(36)
				.foreignKey('keycloak.client', 'id', {
					name: 'fk_1burs8pb4ouj97h5wuppahv9f',
				})
				.index(),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['clientId', 'value'], 'constraint_redirect_uris'),
	)

	await db.createTable('keycloak.required_action_provider', t => ({
		id: t.varchar(36).primaryKey(),
		alias: t.varchar(255).nullable(),
		name: t.varchar(255).nullable(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_req_act_realm',
			})
			.nullable()
			.index(),
		enabled: t.boolean().default(t.sql`false`),
		defaultAction: t.name('default_action').boolean().default(t.sql`false`),
		providerId: t.name('provider_id').varchar(255).nullable(),
		priority: t.integer().nullable(),
	}))

	await db.createTable(
		'keycloak.resource_server_policy',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255),
			description: t.varchar(255).nullable(),
			type: t.varchar(255),
			decisionStrategy: t.name('decision_strategy').smallint().nullable(),
			logic: t.smallint().nullable(),
			resourceServerId: t
				.name('resource_server_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server', 'id', {
					name: 'fk_frsrpo213xcx4wnkog82ssrfy',
				})
				.index(),
			owner: t.varchar(255).nullable(),
		}),
		t => t.unique(['name', 'resourceServerId'], 'uk_frsrpt700s9v50bu18ws5ha6'),
	)

	await db.createTable(
		'keycloak.resource_server_resource',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255),
			type: t.varchar(255).nullable(),
			iconUri: t.name('icon_uri').varchar(255).nullable(),
			owner: t.varchar(255),
			resourceServerId: t
				.name('resource_server_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server', 'id', {
					name: 'fk_frsrho213xcx4wnkog82ssrfy',
				})
				.index(),
			ownerManagedAccess: t.name('owner_managed_access').boolean().default(t.sql`false`),
			displayName: t.name('display_name').varchar(255).nullable(),
		}),
		t => t.unique(['name', 'owner', 'resourceServerId'], 'uk_frsr6t700s9v50bu18ws5ha6'),
	)

	await db.createTable(
		'keycloak.resource_server_scope',
		t => ({
			id: t.varchar(36).primaryKey(),
			name: t.varchar(255),
			iconUri: t.name('icon_uri').varchar(255).nullable(),
			resourceServerId: t
				.name('resource_server_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server', 'id', {
					name: 'fk_frsrso213xcx4wnkog82ssrfy',
				})
				.index(),
			displayName: t.name('display_name').varchar(255).nullable(),
		}),
		t => t.unique(['name', 'resourceServerId'], 'uk_frsrst700s9v50bu18ws5ha6'),
	)

	await db.createTable(
		'keycloak.scope_mapping',
		t => ({
			clientId: t.name('client_id').varchar(36).foreignKey('keycloak.client', 'id', {
				name: 'fk_ouse064plmlr732lxjcn1q5f1',
			}),
			roleId: t.name('role_id').varchar(36).index(),
		}),
		t => t.primaryKey(['clientId', 'roleId'], 'constraint_81'),
	)

	await db.createTable('states', t => ({
		abbreviation: t.enum('public.states_abbreviation_enum').unique(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.enum('public.states_label_enum').unique(),
	}))

	await db.createTable(
		'tasks_classifications',
		t => ({
			classificationId: t.name('classification_id').uuid().foreignKey('classifications', 'id', {
				name: 'tasks_classifications_classification_id_classifications_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			taskId: t.name('task_id').uuid().foreignKey('tasks', 'id', {
				name: 'tasks_classifications_task_id_tasks_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t => t.primaryKey(['classificationId', 'taskId'], 'tasks_classifications_classification_id_task_id_pk'),
	)

	await db.createTable(
		'tasks_roles',
		t => ({
			roleId: t.name('role_id').uuid().foreignKey('roles', 'id', {
				name: 'tasks_roles_role_id_roles_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			taskId: t.name('task_id').uuid().foreignKey('tasks', 'id', {
				name: 'tasks_roles_task_id_tasks_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t => t.primaryKey(['roleId', 'taskId'], 'tasks_roles_role_id_task_id_pk'),
	)

	await db.createTable('tax_rules', t => ({
		ifThis: t.name('if_this').json(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text(),
		warning: t.text(),
		thenThat: t.name('then_that').enum('public.then_that_enum'),
	}))

	await db.createTable(
		'keycloak.user_attribute',
		t => ({
			name: t.varchar(255),
			value: t.varchar(255).nullable(),
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk_5hrm2vlf9ql5fu043kqepovbr',
				})
				.index(),
			id: t.varchar(36).primaryKey().default(t.sql`'sybase-needs-something-here'::character varying`),
			longValueHash: t.name('long_value_hash').bytea().nullable(),
			longValueHashLowerCase: t.name('long_value_hash_lower_case').bytea().nullable(),
			longValue: t.name('long_value').text().nullable(),
		}),
		t => [
			t.index(['name', 'value'], 'idx_user_attribute_name'),
			t.index(['longValueHash', 'name'], 'user_attr_long_values'),
			t.index(['longValueHashLowerCase', 'name'], 'user_attr_long_values_lower_case'),
		],
	)

	await db.createTable(
		'keycloak.user_consent',
		t => ({
			id: t.varchar(36).primaryKey(),
			clientId: t.name('client_id').varchar(255).nullable(),
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk_grntcsnt_user',
				})
				.index(),
			createdDate: t.name('created_date').bigint().nullable(),
			lastUpdatedDate: t.name('last_updated_date').bigint().nullable(),
			clientStorageProvider: t.name('client_storage_provider').varchar(36).nullable(),
			externalClientId: t.name('external_client_id').varchar(255).nullable(),
		}),
		t => [
			t.unique(['clientStorageProvider', 'externalClientId', 'userId'], 'uk_external_consent'),
			t.unique(['clientId', 'userId'], 'uk_local_consent'),
		],
	)

	await db.createTable('keycloak.user_federation_provider', t => ({
		id: t.varchar(36).primaryKey(),
		changedSyncPeriod: t.name('changed_sync_period').integer().nullable(),
		displayName: t.name('display_name').varchar(255).nullable(),
		fullSyncPeriod: t.name('full_sync_period').integer().nullable(),
		lastSync: t.name('last_sync').integer().nullable(),
		priority: t.integer().nullable(),
		providerName: t.name('provider_name').varchar(255).nullable(),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_1fj32f6ptolw2qy60cd8n01e8',
			})
			.nullable()
			.index(),
	}))

	await db.createTable(
		'keycloak.user_group_membership',
		t => ({
			groupId: t.name('group_id').varchar(36),
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk_user_group_user',
				})
				.index(),
			membershipType: t.name('membership_type').varchar(255),
		}),
		t => t.primaryKey(['groupId', 'userId'], 'constraint_user_group'),
	)

	await db.createTable(
		'keycloak.user_required_action',
		t => ({
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk_6qj3w1jw9cvafhe19bwsiuvmd',
				})
				.index(),
			requiredAction: t.name('required_action').varchar(255).default(t.sql`' '::character varying`),
		}),
		t => t.primaryKey(['userId', 'requiredAction'], 'constraint_required_action'),
	)

	await db.createTable(
		'keycloak.user_role_mapping',
		t => ({
			roleId: t.name('role_id').varchar(255),
			userId: t
				.name('user_id')
				.varchar(36)
				.foreignKey('keycloak.user_entity', 'id', {
					name: 'fk_c4fqv34p1mbylloxang7b1q3l',
				})
				.index(),
		}),
		t => t.primaryKey(['roleId', 'userId'], 'constraint_c'),
	)

	await db.createTable(
		'keycloak.web_origins',
		t => ({
			clientId: t
				.name('client_id')
				.varchar(36)
				.foreignKey('keycloak.client', 'id', {
					name: 'fk_lojpho213xcx4wnkog82ssrfy',
				})
				.index(),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['clientId', 'value'], 'constraint_web_origins'),
	)

	await db.createTable(
		'xml_types_configs',
		{
			noPrimaryKey: true,
		},
		t => ({
			xmlTypeId: t
				.name('xml_type_id')
				.uuid()
				.foreignKey('xml_types', 'id', {
					name: 'xml_types_configs_xml_type_id_xml_types_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.default(t.sql`gen_random_uuid()`),
			from: t.text(),
			to: t.enum('public.xml_configs_to_enum'),
		}),
		t => t.unique(['xmlTypeId', 'to'], 'xml_types_configs_xml_type_id_to_index'),
	)
})

change(async db => {
	await db.createTable(
		'keycloak.associated_policy',
		t => ({
			policyId: t.name('policy_id').varchar(36).foreignKey('keycloak.resource_server_policy', 'id', {
				name: 'fk_frsrpas14xcx4wnkog82ssrfy',
			}),
			associatedPolicyId: t
				.name('associated_policy_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_policy', 'id', {
					name: 'fk_frsr5s213xcx4wnkog82ssrfy',
				})
				.index(),
		}),
		t => t.primaryKey(['policyId', 'associatedPolicyId'], 'constraint_farsrpap'),
	)

	await db.createTable(
		'keycloak.authentication_execution',
		t => ({
			id: t.varchar(36).primaryKey(),
			alias: t.varchar(255).nullable(),
			authenticator: t.varchar(36).nullable(),
			realmId: t
				.name('realm_id')
				.varchar(36)
				.foreignKey('keycloak.realm', 'id', {
					name: 'fk_auth_exec_realm',
				})
				.nullable(),
			flowId: t
				.name('flow_id')
				.varchar(36)
				.foreignKey('keycloak.authentication_flow', 'id', {
					name: 'fk_auth_exec_flow',
				})
				.nullable()
				.index(),
			requirement: t.integer().nullable(),
			priority: t.integer().nullable(),
			authenticatorFlow: t.name('authenticator_flow').boolean().default(t.sql`false`),
			authFlowId: t.name('auth_flow_id').varchar(36).nullable(),
			authConfig: t.name('auth_config').varchar(36).nullable(),
		}),
		t => t.index(['realmId', 'flowId'], 'idx_auth_exec_realm_flow'),
	)

	await db.createTable('cities', t => ({
		ibgeCode: t.name('ibge_code').integer().unique().index().unique(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		stateId: t.name('state_id').uuid().foreignKey('states', 'id', {
			name: 'cities_state_id_states_id_fk',
		}),
		title: t.varchar(),
		rfbCode: t.name('rfb_code').integer().unique().index(),
	}))

	await db.createTable('keycloak.component_config', t => ({
		id: t.varchar(36).primaryKey(),
		componentId: t
			.name('component_id')
			.varchar(36)
			.foreignKey('keycloak.component', 'id', {
				name: 'fk_component_config',
			})
			.index(),
		name: t.varchar(255),
		value: t.text().nullable(),
	}))

	await db.createTable(
		'keycloak.composite_role',
		t => ({
			composite: t
				.varchar(36)
				.foreignKey('keycloak.keycloak_role', 'id', {
					name: 'fk_a63wvekftu8jo1pnj81e7mce2',
				})
				.index(),
			childRole: t
				.name('child_role')
				.varchar(36)
				.foreignKey('keycloak.keycloak_role', 'id', {
					name: 'fk_gr7thllb9lu8q4vqa4524jjy8',
				})
				.index(),
		}),
		t => t.primaryKey(['composite', 'childRole'], 'constraint_composite_role'),
	)

	await db.createTable(
		'keycloak.identity_provider_config',
		t => ({
			identityProviderId: t.name('identity_provider_id').varchar(36).foreignKey('keycloak.identity_provider', 'internal_id', {
				name: 'fkdc4897cf864c4e43',
			}),
			value: t.text().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['identityProviderId', 'name'], 'constraint_d'),
	)

	await db.createTable(
		'keycloak.idp_mapper_config',
		t => ({
			idpMapperId: t.name('idp_mapper_id').varchar(36).foreignKey('keycloak.identity_provider_mapper', 'id', {
				name: 'fk_idpmconfig',
			}),
			value: t.text().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['idpMapperId', 'name'], 'constraint_idpmconfig'),
	)

	await db.createTable(
		'keycloak.policy_config',
		t => ({
			policyId: t.name('policy_id').varchar(36).foreignKey('keycloak.resource_server_policy', 'id', {
				name: 'fkdc34197cf864c4e43',
			}),
			name: t.varchar(255),
			value: t.text().nullable(),
		}),
		t => t.primaryKey(['policyId', 'name'], 'constraint_dpc'),
	)

	await db.createTable(
		'keycloak.protocol_mapper_config',
		t => ({
			protocolMapperId: t.name('protocol_mapper_id').varchar(36).foreignKey('keycloak.protocol_mapper', 'id', {
				name: 'fk_pmconfig',
			}),
			value: t.text().nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['protocolMapperId', 'name'], 'constraint_pmconfig'),
	)

	await db.createTable('keycloak.resource_attribute', t => ({
		id: t.varchar(36).primaryKey().default(t.sql`'sybase-needs-something-here'::character varying`),
		name: t.varchar(255),
		value: t.varchar(255).nullable(),
		resourceId: t.name('resource_id').varchar(36).foreignKey('keycloak.resource_server_resource', 'id', {
			name: 'fk_5hrm2vlf9ql5fu022kqepovbr',
		}),
	}))

	await db.createTable(
		'keycloak.resource_policy',
		t => ({
			resourceId: t.name('resource_id').varchar(36).foreignKey('keycloak.resource_server_resource', 'id', {
				name: 'fk_frsrpos53xcx4wnkog82ssrfy',
			}),
			policyId: t
				.name('policy_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_policy', 'id', {
					name: 'fk_frsrpp213xcx4wnkog82ssrfy',
				})
				.index(),
		}),
		t => t.primaryKey(['resourceId', 'policyId'], 'constraint_farsrpp'),
	)

	await db.createTable(
		'keycloak.resource_scope',
		t => ({
			resourceId: t.name('resource_id').varchar(36).foreignKey('keycloak.resource_server_resource', 'id', {
				name: 'fk_frsrpos13xcx4wnkog82ssrfy',
			}),
			scopeId: t
				.name('scope_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_scope', 'id', {
					name: 'fk_frsrps213xcx4wnkog82ssrfy',
				})
				.index(),
		}),
		t => t.primaryKey(['resourceId', 'scopeId'], 'constraint_farsrsp'),
	)

	await db.createTable(
		'keycloak.resource_server_perm_ticket',
		t => ({
			id: t.varchar(36).primaryKey(),
			owner: t.varchar(255).index(),
			requester: t.varchar(255).index(),
			createdTimestamp: t.name('created_timestamp').bigint(),
			grantedTimestamp: t.name('granted_timestamp').bigint().nullable(),
			resourceId: t.name('resource_id').varchar(36).foreignKey('keycloak.resource_server_resource', 'id', {
				name: 'fk_frsrho213xcx4wnkog83sspmt',
			}),
			scopeId: t
				.name('scope_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_scope', 'id', {
					name: 'fk_frsrho213xcx4wnkog84sspmt',
				})
				.nullable(),
			resourceServerId: t.name('resource_server_id').varchar(36).foreignKey('keycloak.resource_server', 'id', {
				name: 'fk_frsrho213xcx4wnkog82sspmt',
			}),
			policyId: t
				.name('policy_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_policy', 'id', {
					name: 'fk_frsrpo2128cx4wnkog82ssrfy',
				})
				.nullable(),
		}),
		t => t.unique(['owner', 'requester', 'resourceServerId', 'resourceId', 'scopeId'], 'uk_frsr6t700s9v50bu18ws5pmt'),
	)

	await db.createTable(
		'keycloak.resource_uris',
		t => ({
			resourceId: t.name('resource_id').varchar(36).foreignKey('keycloak.resource_server_resource', 'id', {
				name: 'fk_resource_server_uris',
			}),
			value: t.varchar(255),
		}),
		t => t.primaryKey(['resourceId', 'value'], 'constraint_resour_uris_pk'),
	)

	await db.createTable('keycloak.role_attribute', t => ({
		id: t.varchar(36).primaryKey(),
		roleId: t
			.name('role_id')
			.varchar(36)
			.foreignKey('keycloak.keycloak_role', 'id', {
				name: 'fk_role_attribute_id',
			})
			.index(),
		name: t.varchar(255),
		value: t.varchar(255).nullable(),
	}))

	await db.createTable(
		'keycloak.scope_policy',
		t => ({
			scopeId: t.name('scope_id').varchar(36).foreignKey('keycloak.resource_server_scope', 'id', {
				name: 'fk_frsrpass3xcx4wnkog82ssrfy',
			}),
			policyId: t
				.name('policy_id')
				.varchar(36)
				.foreignKey('keycloak.resource_server_policy', 'id', {
					name: 'fk_frsrasp13xcx4wnkog82ssrfy',
				})
				.index(),
		}),
		t => t.primaryKey(['scopeId', 'policyId'], 'constraint_farsrsps'),
	)

	await db.createTable(
		'keycloak.user_consent_client_scope',
		t => ({
			userConsentId: t
				.name('user_consent_id')
				.varchar(36)
				.foreignKey('keycloak.user_consent', 'id', {
					name: 'fk_grntcsnt_clsc_usc',
				})
				.index(),
			scopeId: t.name('scope_id').varchar(36).index(),
		}),
		t => t.primaryKey(['userConsentId', 'scopeId'], 'constraint_grntcsnt_clsc_pm'),
	)

	await db.createTable(
		'keycloak.user_federation_config',
		t => ({
			userFederationProviderId: t.name('user_federation_provider_id').varchar(36).foreignKey('keycloak.user_federation_provider', 'id', {
				name: 'fk_t13hpu1j94r2ebpekr39x5eu5',
			}),
			value: t.varchar(255).nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['userFederationProviderId', 'name'], 'constraint_f9'),
	)

	await db.createTable('keycloak.user_federation_mapper', t => ({
		id: t.varchar(36).primaryKey(),
		name: t.varchar(255),
		federationProviderId: t
			.name('federation_provider_id')
			.varchar(36)
			.foreignKey('keycloak.user_federation_provider', 'id', {
				name: 'fk_fedmapperpm_fedprv',
			})
			.index(),
		federationMapperType: t.name('federation_mapper_type').varchar(255),
		realmId: t
			.name('realm_id')
			.varchar(36)
			.foreignKey('keycloak.realm', 'id', {
				name: 'fk_fedmapperpm_realm',
			})
			.index(),
	}))
})

change(async db => {
	await db.createTable('cnpj', t => ({
		bairro: t.text(),
		capitalSocial: t.name('capital_social').decimal(15, 2).nullable(),
		cep: t.text(),
		cnaeFiscal: t.name('cnae_fiscal').integer().nullable(),
		cnpj: t.text().primaryKey(),
		codigoNaturezaJuridica: t.name('codigo_natureza_juridica').integer().nullable(),
		codigoPorte: t.name('codigo_porte').integer().nullable(),
		complemento: t.text().nullable(),
		dataExclusaoMei: t.name('data_exclusao_mei').timestampNoTZ().nullable(),
		dataExclusaoSimples: t.name('data_exclusao_simples').timestampNoTZ().nullable(),
		dataInicioAtividade: t.name('data_inicio_atividade').timestampNoTZ().nullable(),
		dataOpcaoMei: t.name('data_opcao_mei').timestampNoTZ().nullable(),
		dataOpcaoSimples: t.name('data_opcao_simples').timestampNoTZ().nullable(),
		dataSituacaoCadastral: t.name('data_situacao_cadastral').timestampNoTZ().nullable(),
		dataSituacaoEspecial: t.name('data_situacao_especial').timestampNoTZ().nullable(),
		dddTelefone1: t.name('ddd_telefone1').text().nullable(),
		dddTelefone2: t.name('ddd_telefone2').text().nullable(),
		matrizFilial: t.name('matriz_filial').enum('public.cnpj_matriz_filial_enum'),
		email: t.text().nullable(),
		logradouro: t.text().nullable(),
		motivoSituacaoCadastral: t.name('motivo_situacao_cadastral').integer().nullable(),
		municipio: t.text().nullable(),
		nomeFantasia: t.name('nome_fantasia').text().nullable(),
		numero: t.text(),
		opcaoPeloMei: t.name('opcao_pelo_mei').boolean().nullable().default(t.sql`false`),
		opcaoPeloSimples: t.name('opcao_pelo_simples').boolean().nullable().default(t.sql`false`),
		pais: t.text().nullable(),
		qualificacaoDoResponsavel: t.name('qualificacao_do_responsavel').integer().nullable(),
		razaoSocial: t.name('razao_social').text(),
		situacaoCadastral: t.name('situacao_cadastral').enum('public.cnpj_situacao_cadastral_enum'),
		situacaoEspecial: t.name('situacao_especial').text().nullable(),
		created: t.timestampNoTZ().nullable().default(t.sql`now()`),
		updated: t.timestampNoTZ().nullable().default(t.sql`now()`),
		cityId: t
			.name('city_id')
			.uuid()
			.foreignKey('cities', 'id', {
				name: 'cnpj_city_id_cities_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'SET NULL',
			})
			.nullable(),
		tipoLogradouro: t.name('tipo_logradouro').text().nullable(),
	}))

	await db.createTable(
		'customers',
		t => ({
			bairro: t.text().nullable(),
			cep: t.text().nullable(),
			from: t.timestampNoTZ(),
			id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
			logo: t.text().nullable(),
			logradouro: t.text().nullable(),
			numero: t.text().nullable(),
			title: t.varchar(255),
			unit: t.enum('public.customers_unit_enum'),
			until: t.timestampNoTZ().nullable().index(),
			seafileGroupId: t.name('seafile_group_id').integer().nullable(),
			seafileLibraryId: t.name('seafile_library_id').text().nullable(),
			onvioApiId: t.name('onvio_api_id').text().nullable(),
			onvioApiSecret: t.name('onvio_api_secret').text().nullable(),
			showcaseOnLandingPage: t.name('showcase_on_landing_page').boolean().nullable(),
			solidesApiKey: t.name('solides_api_key').text().nullable(),
			complemento: t.text().nullable(),
			inscricaoMunicipal: t.name('inscricao_municipal').varchar().nullable(),
			cityId: t.name('city_id').uuid().foreignKey('cities', 'id', {
				name: 'customers_city_id_cities_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'SET NULL',
			}),
			tipoLogradouro: t.name('tipo_logradouro').text().nullable(),
			invoicyAccessKey: t.name('invoicy_access_key').text().nullable(),
			emailForInvoices: t.name('email_for_invoices').text().nullable(),
		}),
		t => t.unique(['id', 'title'], 'customers_id_title_index'),
	)

	await db.createTable(
		'keycloak.user_federation_mapper_config',
		t => ({
			userFederationMapperId: t.name('user_federation_mapper_id').varchar(36).foreignKey('keycloak.user_federation_mapper', 'id', {
				name: 'fk_fedmapper_cfg',
			}),
			value: t.varchar(255).nullable(),
			name: t.varchar(255),
		}),
		t => t.primaryKey(['userFederationMapperId', 'name'], 'constraint_fedmapper_cfg_pm'),
	)
})

change(async db => {
	await db.createTable('banking_billet', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'banking_billet_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		internalId: t.name('internal_id').text().unique(),
		status: t.enum('public.banking_billet_status_enum'),
		type: t.enum('public.banking_billet_types_enum'),
		payerName: t.name('payer_name').text(),
		payerTaxNumber: t.name('payer_tax_number').text(),
		dueDate: t.name('due_date').timestampNoTZ(),
		value: t.decimal(),
		billetBankId: t.name('billet_bank_id').text(),
		billetBarCodeRepresentation: t.name('billet_bar_code_representation').text(),
		billetBarCode: t.name('billet_bar_code').text(),
		pixCopyPaste: t.name('pix_copy_paste').text(),
		created: t.timestampNoTZ().default(t.sql`now()`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
		requestId: t.name('request_id').text(),
		referenceDate: t.name('reference_date').timestampNoTZ().nullable(),
		installmentNumber: t.name('installment_number').integer().nullable(),
	}))

	await db.createTable(
		'certidoes',
		t => ({
			created: t.timestampNoTZ().default(t.sql`now()`),
			siteReceiptBase64: t.name('site_receipt_base64').text().nullable(),
			validade: t.timestampNoTZ(),
			siteReceipt: t.name('site_receipt').text().nullable(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'certidoes_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			type: t.enum('public.certidoes_type'),
			result: t.enum('public.certidoes_result'),
			updated: t.timestampNoTZ().default(t.sql`now()`),
		}),
		t => t.primaryKey(['customerId', 'type'], 'certidoes_customer_id_type_pk'),
	)

	await db.createTable(
		'cnpj_cnaes_secundarios',
		t => ({
			cnpj: t.text().foreignKey('cnpj', 'cnpj', {
				name: 'cnpj_cnaes_secundarios_cnpj_cnpj_cnpj_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			codigo: t.integer(),
			descricao: t.text().nullable(),
		}),
		t => t.primaryKey(['cnpj', 'codigo'], 'cnpj_cnaes_secundarios_cnpj_codigo_pk'),
	)

	await db.createTable(
		'cnpj_qsa',
		t => ({
			cnpj: t.text().foreignKey('cnpj', 'cnpj', {
				name: 'cnpj_qsa_cnpj_cnpj_cnpj_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			cnpjCpfDoSocio: t.name('cnpj_cpf_do_socio').text(),
			codigoQualificacaoRepresentanteLegal: t.name('codigo_qualificacao_representante_legal').integer().nullable(),
			codigoQualificacaoSocio: t.name('codigo_qualificacao_socio').integer().nullable(),
			cpfRepresentanteLegal: t.name('cpf_representante_legal').text().nullable(),
			identificadorDeSocio: t.name('identificador_de_socio').integer().nullable(),
			nomeRepresentanteLegal: t.name('nome_representante_legal').text().nullable(),
			nomeSocio: t.name('nome_socio').text().nullable(),
			percentualCapitalSocial: t.name('percentual_capital_social').integer().nullable(),
		}),
		t => t.primaryKey(['cnpj', 'cnpjCpfDoSocio'], 'cnpj_qsa_cnpj_cnpj_cpf_do_socio_pk'),
	)

	await db.createTable(
		'cte',
		t => ({
			accessKey: t.name('access_key').text(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'cte_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ().index(),
			type: t.enum('public.cte_type').index(),
			xml: t.text(),
			nsu: t.integer().nullable(),
			sentToDominio: t.name('sent_to_dominio').boolean().default(t.sql`false`).index(),
			erpKey: t.name('erp_key').integer().nullable(),
			number: t.integer().nullable(),
			value: t.decimal().nullable(),
			dispatcherName: t.name('dispatcher_name').text().nullable(),
			issuerCpfCnpj: t.name('issuer_cpf_cnpj').text().nullable(),
			issuerIe: t.name('issuer_ie').text().nullable(),
			issuerName: t.name('issuer_name').text().nullable(),
			issuerCity: t.name('issuer_city').text().nullable(),
			issuerUf: t.name('issuer_uf').text().nullable(),
			issuerCep: t.name('issuer_cep').text().nullable(),
			issuerAddress: t.name('issuer_address').text().nullable(),
			issuerNumber: t.name('issuer_number').text().nullable(),
			issuerComplement: t.name('issuer_complement').text().nullable(),
			issuerCountry: t.name('issuer_country').text().nullable(),
			issuerNeighborhood: t.name('issuer_neighborhood').text().nullable(),
			shipperCpfCnpj: t.name('shipper_cpf_cnpj').text().nullable(),
			dispatcherCpfCnpj: t.name('dispatcher_cpf_cnpj').text().nullable(),
			receiverCpfCnpj: t.name('receiver_cpf_cnpj').text().nullable(),
			destinationCpfCnpj: t.name('destination_cpf_cnpj').text().nullable(),
			otherCpfCnpj: t.name('other_cpf_cnpj').text().nullable(),
			isTomador: t.name('is_tomador').boolean().default(t.sql`false`),
		}),
		t => t.primaryKey(['accessKey', 'customerId', 'type'], 'cte_customer_id_access_key_type_pk'),
	)

	await db.createTable(
		'cte_events',
		t => ({
			accessKey: t.name('access_key').text().index(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'cte_events_customer_id_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.index(),
			date: t.timestampNoTZ().index(),
			eventNumber: t.name('event_number').integer(),
			eventType: t.name('event_type').enum('public.cte_events_event_type'),
			nsu: t.integer().nullable(),
			xml: t.text(),
		}),
		t =>
			t.primaryKey(['accessKey', 'customerId', 'eventNumber', 'eventType'], 'cte_events_customer_id_access_key_event_number_event_type_pk'),
	)

	await db.createTable(
		'cte_os',
		t => ({
			accessKey: t.name('access_key').text(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'cte_os_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ().index(),
			nsu: t.integer().nullable(),
			sentToDominio: t.name('sent_to_dominio').boolean().default(t.sql`false`),
			type: t.enum('public.cte_os_type'),
			xml: t.text(),
			erpKey: t.name('erp_key').integer().nullable(),
			issuerCpfCnpj: t.name('issuer_cpf_cnpj').text().nullable(),
			issuerIe: t.name('issuer_ie').text().nullable(),
			issuerName: t.name('issuer_name').text().nullable(),
			issuerCity: t.name('issuer_city').text().nullable(),
			issuerUf: t.name('issuer_uf').text().nullable(),
			issuerCep: t.name('issuer_cep').text().nullable(),
			issuerAddress: t.name('issuer_address').text().nullable(),
			issuerNumber: t.name('issuer_number').text().nullable(),
			issuerComplement: t.name('issuer_complement').text().nullable(),
			issuerCountry: t.name('issuer_country').text().nullable(),
			issuerNeighborhood: t.name('issuer_neighborhood').text().nullable(),
			recipientCpfCnpj: t.name('recipient_cpf_cnpj').text().nullable(),
			recipientName: t.name('recipient_name').text().nullable(),
			recipientIe: t.name('recipient_ie').text().nullable(),
			recipientCity: t.name('recipient_city').text().nullable(),
			recipientUf: t.name('recipient_uf').text().nullable(),
			recipientCep: t.name('recipient_cep').text().nullable(),
			recipientAddress: t.name('recipient_address').text().nullable(),
			recipientNumber: t.name('recipient_number').text().nullable(),
			recipientComplement: t.name('recipient_complement').text().nullable(),
			recipientCountry: t.name('recipient_country').text().nullable(),
			number: t.integer().nullable(),
			value: t.decimal().nullable(),
		}),
		t => t.primaryKey(['accessKey', 'customerId', 'type'], 'cte_os_customer_id_access_key_type_pk'),
	)

	await db.createTable('customers_nfse_settings', t => ({
		customerId: t.name('customer_id').uuid().primaryKey().foreignKey('customers', 'id', {
			name: 'customers_nfse_settings_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		aliquota: t.integer(),
		codigoTributacaoMunicipio: t.name('codigo_tributacao_municipio').text(),
		descricaoServico: t.name('descricao_servico').text(),
		issRetido: t.name('iss_retido').text(),
		itemListaServico: t.name('item_lista_servico').text(),
		optanteSimplesNacional: t.name('optante_simples_nacional').text(),
		regimeEspecialTributacao: t.name('regime_especial_tributacao').type('bpchar'),
	}))

	await db.createTable(
		'customers_partners',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'customers_partners_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			name: t.varchar().nullable(),
			taxNumber: t.name('tax_number').varchar(),
			rfbQualification: t.name('rfb_qualification').enum('public.customers_partners_qualification_enum'),
			isResponsibleForRfb: t.name('is_responsible_for_rfb').boolean().nullable().default(t.sql`false`),
		}),
		t => t.primaryKey(['customerId', 'taxNumber'], 'customers_partners_customer_id_tax_number_pk'),
	)

	await db.createTable(
		'customers_payroll_configs',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'customers_payroll_configs_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			from: t.timestampNoTZ(),
			until: t.timestampNoTZ(),
			hasActivities: t.name('has_activities').boolean().default(t.sql`true`),
			advance: t.boolean().default(t.sql`true`),
			fgts: t.boolean().default(t.sql`true`),
		}),
		t => t.primaryKey(['customerId', 'from', 'until'], 'customers_payroll_configs_customer_id_from_until_pk'),
	)

	// customers_secrets table removed - now using vault.secrets with metadata

	await db.createTable(
		'customers_tax_numbers',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'customers_tax_numbers_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			type: t.enum('public.customers_tax_numbers_type_enum'),
			taxNumber: t.name('tax_number').varchar(),
			details: t.varchar().nullable(),
			stateId: t
				.name('state_id')
				.uuid()
				.foreignKey('states', 'id', {
					name: 'customers_tax_numbers_state_id_states_id_fk',
				})
				.nullable(),
		}),
		t => t.primaryKey(['customerId', 'type', 'taxNumber'], 'customers_tax_numbers_customer_id_type_tax_number_pk'),
	)

	await db.createTable(
		'customers_tax_passwords',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'customers_tax_passwords_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			type: t.enum('public.customers_tax_passwords_type_enum'),
			login: t.text(),
			password: t.text(),
		}),
		t => t.primaryKey(['customerId', 'type', 'login'], 'customers_tax_passwords_customer_id_type_login_pk'),
	)

	await db.createTable('email', t => ({
		customerId: t
			.name('customer_id')
			.uuid()
			.foreignKey('customers', 'id', {
				name: 'email_customer_id_customers_id_fk',
			})
			.nullable()
			.index(),
		from: t.text(),
		content: t.text().nullable(),
		sendgridId: t.name('sendgrid_id').text().nullable(),
		subject: t.text(),
		sent: t.timestampNoTZ().default(t.sql`now()`).index(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		templateId: t
			.name('template_id')
			.uuid()
			.foreignKey('templates', 'id', {
				name: 'email_template_id_templates_id_fk',
			})
			.nullable(),
		identifier: t.text().nullable(),
	}))

	await db.createTable('installments', t => ({
		creditor: t.text(),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'installments_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		from: t.timestampNoTZ(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		misc: t.text(),
		months: t.integer(),
		number: t.text(),
		sent: t.json(),
		status: t.text(),
		url: t.text().nullable(),
		users: t.json(),
	}))

	await db.createTable(
		'irpf_docs',
		t => ({
			cpf: t.text(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'irpf_docs_customer_id_customers_id_fk',
				})
				.nullable(),
			data: t.text(),
			email: t.text().nullable(),
			fileId: t.name('file_id').uuid(),
			id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
			name: t.text(),
			sentByEmailDate: t.name('sent_by_email_date').timestampNoTZ().nullable(),
			sentByEmailId: t.name('sent_by_email_id').text().nullable(),
			competencia: t.timestampNoTZ().nullable(),
		}),
		t => t.unique(['customerId', 'competencia', 'cpf'], 'irpf_docs_customer_id_competencia_cpf_index'),
	)

	await db.createTable(
		'movements',
		{
			noPrimaryKey: true,
		},
		t => ({
			customerId: t
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'movements_customerId_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.unique(),
			transformAccounts: t.json(),
		}),
	)

	await db.createTable(
		'nfe',
		t => ({
			accessKey: t.name('access_key').text(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfe_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			destinacaoInformada: t.name('destinacao_informada').boolean().nullable(),
			date: t.timestampNoTZ().index(),
			entrada: t.timestampNoTZ().nullable(),
			type: t.enum('public.nfe_type_enum').index(),
			sentToDominio: t.name('sent_to_dominio').boolean().default(t.sql`false`).index(),
			nsu: t.integer().nullable(),
			xml: t.text(),
			nfeProtocolStatus: t.name('nfe_protocol_status').enum('public.nfe_protocol_status_enum'),
			isDenied: t.name('is_denied').enum('public.nfe_is_denied_enum'),
			erpKey: t.name('erp_key').integer().nullable(),
			issuerCpfCnpj: t.name('issuer_cpf_cnpj').text(),
			issuerIe: t.name('issuer_ie').text().nullable(),
			issuerName: t.name('issuer_name').text().nullable(),
			issuerCity: t.name('issuer_city').text().nullable(),
			issuerUf: t.name('issuer_uf').text().nullable(),
			issuerCep: t.name('issuer_cep').text().nullable(),
			issuerAddress: t.name('issuer_address').text().nullable(),
			issuerNumber: t.name('issuer_number').text().nullable(),
			issuerComplement: t.name('issuer_complement').text().nullable(),
			issuerCountry: t.name('issuer_country').text().nullable(),
			issuerNeighborhood: t.name('issuer_neighborhood').text().nullable(),
			receiverCpfCnpj: t.name('receiver_cpf_cnpj').text().nullable(),
			receiverIe: t.name('receiver_ie').text().nullable(),
			receiverName: t.name('receiver_name').text().nullable(),
			receiverCity: t.name('receiver_city').text().nullable(),
			receiverUf: t.name('receiver_uf').text().nullable(),
			receiverCep: t.name('receiver_cep').text().nullable(),
			receiverAddress: t.name('receiver_address').text().nullable(),
			receiverNumber: t.name('receiver_number').text().nullable(),
			receiverComplement: t.name('receiver_complement').text().nullable(),
			receiverCountry: t.name('receiver_country').text().nullable(),
			receiverNeighborhood: t.name('receiver_neighborhood').text().nullable(),
			number: t.integer().nullable(),
			value: t.decimal().nullable(),
			paymentType: t.name('payment_type').text().nullable(),
			freightType: t.name('freight_type').text().nullable(),
			issuerSuframa: t.name('issuer_suframa').text().nullable(),
			isTenantContabilidadeAuthorized: t
				.name('is_tenant_contabilidade_authorized')
				.enum('public.nfe_is_tenant_contabilidade_authorized_enum'),
		}),
		t => t.primaryKey(['accessKey', 'customerId', 'type'], 'nfe_customer_id_access_key_type_pk'),
	)

	await db.createTable('nfe_cte_ult_nsu', t => ({
		customerId: t.name('customer_id').uuid().primaryKey().foreignKey('customers', 'id', {
			name: 'nfe_cte_ult_nsu_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		nfe: t.integer().nullable(),
		cte: t.integer().nullable(),
	}))

	await db.createTable(
		'nfe_invalidation',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfe_invalidation_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ(),
			json: t.json().nullable(),
			protocol: t.text(),
			xml: t.text(),
			number: t.integer().nullable(),
		}),
		t => t.primaryKey(['customerId', 'protocol'], 'nfe_invalidation_customer_id_protocol_pk'),
	)

	await db.createTable(
		'nfe_nsus_to_ignore',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfe_nsus_to_ignore_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			nsu: t.integer(),
			date: t.timestampNoTZ(),
		}),
		t => t.primaryKey(['customerId', 'nsu'], 'nfe_nsus_to_ignore_customer_id_nsu_pk'),
	)

	await db.createTable(
		'nfe_products',
		t => ({
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfe_products_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			accessKey: t.name('access_key').text(),
			productIndex: t.name('product_index').integer(),
			ncm: t.type('bpchar'),
			description: t.text(),
			cfop: t.type('bpchar'),
			code: t.varchar(),
			quantity: t.decimal(),
			unit: t.varchar(),
			freight: t.decimal().default(t.sql`'0'::numeric`),
			otherExpenses: t.name('other_expenses').decimal().default(t.sql`'0'::numeric`),
			discount: t.decimal().default(t.sql`'0'::numeric`),
			bcIcms: t.name('bc_icms').decimal().default(t.sql`'0'::numeric`),
			aliquotaIcms: t.name('aliquota_icms').decimal().default(t.sql`'0'::numeric`),
			icms: t.decimal().default(t.sql`'0'::numeric`),
			cstIcms: t.name('cst_icms').varchar(),
			bcIpi: t.name('bc_ipi').decimal().default(t.sql`'0'::numeric`),
			aliquotaIpi: t.name('aliquota_ipi').decimal().default(t.sql`'0'::numeric`),
			ipi: t.decimal().default(t.sql`'0'::numeric`),
			cstIpi: t.name('cst_ipi').varchar(),
			bcPis: t.name('bc_pis').decimal().default(t.sql`'0'::numeric`),
			aliquotaPis: t.name('aliquota_pis').decimal().default(t.sql`'0'::numeric`),
			pis: t.decimal().default(t.sql`'0'::numeric`),
			cstPis: t.name('cst_pis').varchar(),
			bcCofins: t.name('bc_cofins').decimal().default(t.sql`'0'::numeric`),
			aliquotaCofins: t.name('aliquota_cofins').decimal().default(t.sql`'0'::numeric`),
			cofins: t.decimal().default(t.sql`'0'::numeric`),
			cstCofins: t.name('cst_cofins').varchar(),
			unitValue: t.name('unit_value').decimal().default(t.sql`'0'::numeric`),
			totalValue: t.name('total_value').decimal().default(t.sql`'0'::numeric`),
			destinationCfop: t.name('destination_cfop').type('bpchar'),
			destinationType: t.name('destination_type').varchar().nullable(),
			destinationCode: t.name('destination_code').varchar().nullable(),
			destinationBcIcms: t.name('destination_bc_icms').decimal().default(t.sql`'0'::numeric`),
			destinationIcms: t.name('destination_icms').decimal().default(t.sql`'0'::numeric`),
			destinationBcIpi: t.name('destination_bc_ipi').decimal().default(t.sql`'0'::numeric`),
			destinationIpi: t.name('destination_ipi').decimal().default(t.sql`'0'::numeric`),
			destinationBcPis: t.name('destination_bc_pis').decimal().default(t.sql`'0'::numeric`),
			destinationPis: t.name('destination_pis').decimal().default(t.sql`'0'::numeric`),
			destinationBcCofins: t.name('destination_bc_cofins').decimal().default(t.sql`'0'::numeric`),
			destinationCofins: t.name('destination_cofins').decimal().default(t.sql`'0'::numeric`),
			insurance: t.decimal().default(t.sql`'0'::numeric`),
			bcIcmsSt: t.name('bc_icms_st').decimal().default(t.sql`'0'::numeric`),
			aliquotaIcmsSt: t.name('aliquota_icms_st').decimal().default(t.sql`'0'::numeric`),
			icmsSt: t.name('icms_st').decimal().default(t.sql`'0'::numeric`),
		}),
		t => t.primaryKey(['customerId', 'accessKey', 'productIndex'], 'nfe_products_customer_id_access_key_product_index_pk'),
	)

	await db.createTable('other_incomes', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'other_incomes_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		date: t.timestampNoTZ(),
		otherIncomesType: t.name('other_incomes_type').enum('public.other_incomes_types_enum'),
		description: t.text(),
		value: t.decimal().default(t.sql`'0'::numeric`),
	}))

	await db.createTable(
		'products',
		t => ({
			barCode: t.name('bar_code').text().nullable(),
			cest: t.text().nullable(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'products_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			exIpi: t.name('ex_ipi').text().nullable(),
			gen: t.text().nullable(),
			icms: t.text().nullable(),
			id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
			importRules: t.name('import_rules').json().default(t.sql`'{}'::jsonb`),
			internalCode: t.name('internal_code').text(),
			lst: t.text().nullable(),
			ncm: t.text(),
			title: t.text(),
			type: t.enum('public.destinacao'),
			unity: t.text(),
		}),
		t => t.unique(['customerId', 'internalCode'], 'products_customer_id_internal_code_index'),
	)

	await db.createTable('rents', t => ({
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'rents_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		discount: t.decimal().default(t.sql`'0'::numeric`),
		from: t.timestampNoTZ(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		misc: t.text().nullable(),
		status: t.enum('public.rents_status_enum'),
		value: t.decimal().default(t.sql`'0'::numeric`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
	}))

	await db.createTable('tax_documents', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'tax_documents_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		type: t.enum('public.tax_documents_types_enum'),
		cityId: t.name('city_id').uuid().foreignKey('cities', 'id', {
			name: 'tax_documents_city_id_cities_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		darfCodeId: t
			.name('darf_code_id')
			.uuid()
			.foreignKey('darf_codes', 'id', {
				name: 'tax_documents_darf_code_id_darf_codes_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			})
			.nullable(),
		quota: t.integer().nullable(),
		value: t.decimal(13, 2),
		created: t.timestampNoTZ().default(t.sql`now()`),
		details: t.text(),
		referencePeriod: t.name('reference_period').timestampNoTZ(),
		dueDate: t.name('due_date').timestampNoTZ(),
		fine: t.decimal(13, 2).default(t.sql`0.00`),
		interest: t.decimal(13, 2).default(t.sql`0.00`),
	}))

	await db.createTable(
		'tax_payments',
		t => ({
			id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'tax_payments_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			documentNumber: t.name('document_number').varchar(),
			documentTypeCode: t.name('document_type_code').varchar(),
			documentTypeDescription: t.name('document_type_description').text(),
			documentTypeShortDescription: t.name('document_type_short_description').text(),
			documentReferenceDate: t.name('document_reference_date').timestampNoTZ(),
			documentPaymentDate: t.name('document_payment_date').timestampNoTZ(),
			documentDueDate: t.name('document_due_date').timestampNoTZ(),
			documentRevenueCode: t.name('document_revenue_code').varchar(),
			documentRevenueDescription: t.name('document_revenue_description').text().nullable(),
			documentTotalValue: t.name('document_total_value').decimal(),
			documentMainValue: t.name('document_main_value').decimal(),
			documentFineValue: t.name('document_fine_value').decimal().nullable(),
			documentInterestValue: t.name('document_interest_value').decimal().nullable(),
			documentTotalBalanceValue: t.name('document_total_balance_value').decimal().nullable(),
			documentMainBalanceValue: t.name('document_main_balance_value').decimal().nullable(),
			documentFineBalanceValue: t.name('document_fine_balance_value').decimal().nullable(),
			documentInterestBalanceValue: t.name('document_interest_balance_value').decimal().nullable(),
			documentReference: t.name('document_reference').varchar().nullable(),
			detailSequential: t.name('detail_sequential').varchar(),
			detailRevenueCode: t.name('detail_revenue_code').varchar(),
			detailRevenueDescription: t.name('detail_revenue_description').text(),
			detailExtensionCode: t.name('detail_extension_code').varchar().nullable(),
			detailExtensionDescription: t.name('detail_extension_description').text().nullable(),
			detailReferenceDate: t.name('detail_reference_date').timestampNoTZ(),
			detailDueDate: t.name('detail_due_date').timestampNoTZ(),
			detailTotalValue: t.name('detail_total_value').decimal(),
			detailMainValue: t.name('detail_main_value').decimal(),
			detailFineValue: t.name('detail_fine_value').decimal().nullable(),
			detailInterestValue: t.name('detail_interest_value').decimal().nullable(),
			detailTotalBalanceValue: t.name('detail_total_balance_value').decimal().nullable(),
			detailMainBalanceValue: t.name('detail_main_balance_value').decimal().nullable(),
			detailFineBalanceValue: t.name('detail_fine_balance_value').decimal().nullable(),
			detailInterestBalanceValue: t.name('detail_interest_balance_value').decimal().nullable(),
			created: t.timestampNoTZ().default(t.sql`now()`),
			updated: t.timestampNoTZ().default(t.sql`now()`),
		}),
		t => t.unique(['documentNumber', 'detailSequential'], 'tax_payments_document_number_detail_sequential_index'),
	)

	await db.createTable('testimony', t => ({
		author: t.text(),
		date: t.timestampNoTZ(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		text: t.text(),
		title: t.text(),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'testimony_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
	}))

	await db.createTable('users', t => ({
		cpf: t.text(),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		lastLogin: t.name('last_login').timestampNoTZ().nullable(),
		profilePicture: t.name('profile_picture').text().default(t.sql`''::text`),
		role: t.text().nullable(),
		welcomeEmailSent: t.name('welcome_email_sent').boolean().default(t.sql`false`),
		welcomeMessageShown: t.name('welcome_message_shown').boolean().default(t.sql`false`),
		selectedCustomerId: t
			.name('selected_customer_id')
			.uuid()
			.foreignKey('customers', 'id', {
				name: 'users_selected_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			})
			.nullable(),
		selectedFromDate: t.name('selected_from_date').timestampNoTZ(),
		selectedUntilDate: t.name('selected_until_date').timestampNoTZ(),
		email: t.text().unique(),
		enabled: t.boolean(),
		name: t.text(),
		roles: t.json().nullable(),
		phone: t.varchar(15).nullable(),
	}))
})

change(async db => {
	await db.createTable(
		'email_messages_recipients',
		t => ({
			emailId: t.name('email_id').uuid().foreignKey('email', 'id', {
				name: 'email_messages_recipients_email_id_email_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			type: t.enum('public.email_messages_recipients_type_enum'),
			address: t.text(),
		}),
		t => t.primaryKey(['emailId', 'type', 'address'], 'email_messages_recipients_email_id_type_address_pk'),
	)

	await db.createTable('files', t => ({
		customerId: t
			.name('customer_id')
			.uuid()
			.foreignKey('customers', 'id', {
				name: 'files_customer_id_customers_id_fk',
			})
			.nullable(),
		created: t.timestampNoTZ().default(t.sql`now()`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		name: t.text(),
		processed: t.boolean(),
		type: t.enum('public.files_type_enum'),
		userId: t
			.name('user_id')
			.uuid()
			.foreignKey('users', 'id', {
				name: 'files_user_id_users_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'SET NULL',
			})
			.nullable(),
		classificationId: t
			.name('classification_id')
			.uuid()
			.foreignKey('classifications', 'id', {
				name: 'files_classification_id_classifications_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'SET NULL',
			})
			.nullable(),
		processedData: t.name('processed_data').json().nullable(),
		hash: t.type('bpchar').unique(),
		packId: t.name('pack_id').uuid().nullable(),
	}))

	await db.createTable('one_on_one', t => ({
		comments: t.text(),
		date: t.timestampNoTZ(),
		employeeId: t.name('employee_id').uuid().foreignKey('users', 'id', {
			name: 'one_on_one_employee_id_users_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		managerId: t.name('manager_id').uuid().foreignKey('users', 'id', {
			name: 'one_on_one_manager_id_users_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
	}))

	await db.createTable(
		'permissions',
		t => ({
			customerId: t.uuid().foreignKey('customers', 'id', {
				name: 'permissions_customerId_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			userId: t.uuid().foreignKey('users', 'id', {
				name: 'permissions_userId_users_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t => t.primaryKey(['customerId', 'userId'], 'permissions_customerid_userid_pk'),
	)

	await db.createTable(
		'rents_history',
		t => ({
			rentId: t.name('rent_id').uuid().foreignKey('rents', 'id', {
				name: 'rents_history_rent_id_rents_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ(),
			value: t.decimal(),
		}),
		t => t.primaryKey(['rentId', 'date'], 'rents_history_rent_id_date_pk'),
	)

	await db.createTable('rents_owners', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		rentId: t.name('rent_id').uuid().foreignKey('rents', 'id', {
			name: 'rents_owners_rent_id_rents_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		name: t.text(),
		cpf: t.type('bpchar'),
	}))

	await db.createTable(
		'rents_recipients',
		t => ({
			rentId: t.name('rent_id').uuid().foreignKey('rents', 'id', {
				name: 'rents_recipients_rent_id_rents_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			userId: t.name('user_id').uuid().foreignKey('users', 'id', {
				name: 'rents_recipients_user_id_users_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t => t.primaryKey(['rentId', 'userId'], 'rents_recipients_rent_id_user_id_pk'),
	)

	await db.createTable(
		'users_roles',
		t => ({
			userId: t.name('user_id').uuid().foreignKey('users', 'id', {
				name: 'users_roles_user_id_users_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			roleId: t.name('role_id').uuid(),
		}),
		t => t.primaryKey(['userId', 'roleId'], 'users_roles_user_id_role_id_pk'),
	)

	await db.createTable(
		'users_tags',
		t => ({
			userId: t.name('user_id').uuid().foreignKey('users', 'id', {
				name: 'users_tags_user_id_users_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			roleId: t.name('role_id').uuid().foreignKey('tags', 'id', {
				name: 'users_tags_role_id_tags_id_fk',
				onUpdate: 'RESTRICT',
				onDelete: 'RESTRICT',
			}),
		}),
		t => t.primaryKey(['userId', 'roleId'], 'users_tags_user_id_role_id_pk'),
	)
})

change(async db => {
	await db.createTable('alvaras', t => ({
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'alvaras_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
			name: 'alvaras_file_id_files_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		type: t.text(),
		validTo: t.name('valid_to').timestampNoTZ(),
	}))

	await db.createTable('assets', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		title: t.text(),
		description: t.text(),
		fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
			name: 'assets_file_id_files_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		created: t.timestampNoTZ().default(t.sql`now()`),
		updated: t.timestampNoTZ().default(t.sql`now()`),
	}))

	await db.createTable('documents_files', t => ({
		id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
		documentId: t.name('document_id').uuid().foreignKey('documents', 'id', {
			name: 'documents_files_document_id_documents_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
			name: 'documents_files_file_id_files_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
			name: 'documents_files_customer_id_customers_id_fk',
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE',
		}),
		path: t.text(),
	}))

	await db.createTable(
		'email_attachments',
		t => ({
			emailId: t.name('email_id').uuid().foreignKey('email', 'id', {
				name: 'email_attachments_email_id_email_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'email_attachments_file_id_files_id_fk',
			}),
		}),
		t => t.primaryKey(['emailId', 'fileId'], 'email_attachments_email_id_file_id_pk'),
	)

	await db.createTable(
		'files_tasks_flow',
		t => ({
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'files_tasks_flow_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'files_tasks_flow_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			competencia: t.timestampNoTZ(),
			error: t.boolean().default(t.sql`false`),
		}),
		t => t.primaryKey(['fileId', 'customerId'], 'files_tasks_flow_customer_id_file_id_pk'),
	)

	await db.createTable(
		'files_taxes_flow',
		t => ({
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'files_taxes_flow_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'files_taxes_flow_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			competencia: t.timestampNoTZ(),
			valor: t.decimal(),
			vencimento: t.timestampNoTZ(),
			error: t.boolean().default(t.sql`false`),
			barCode: t.name('bar_code').varchar().nullable(),
		}),
		t => t.primaryKey(['fileId', 'customerId'], 'files_taxes_flow_customer_id_file_id_pk'),
	)

	await db.createTable(
		'nfe_entrega_events',
		t => ({
			accessKey: t.name('access_key').text().index(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'nfe_entrega_events_customer_id_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.index(),
			date: t.timestampNoTZ().index(),
			eventNumber: t.name('event_number').integer(),
			eventType: t.name('event_type').integer(),
			nsu: t.integer().nullable(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfe_entrega_events_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t =>
			t.primaryKey(
				['accessKey', 'customerId', 'eventNumber', 'eventType'],
				'nfe_entrega_events_customer_id_access_key_event_number_event_ty',
			),
	)

	await db.createTable(
		'nfe_events',
		t => ({
			accessKey: t.name('access_key').text().index(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'nfe_events_customer_id_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.index(),
			date: t.timestampNoTZ().index(),
			nsu: t.integer().nullable(),
			eventNumber: t.name('event_number').integer(),
			eventType: t.name('event_type').integer(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfe_events_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t =>
			t.primaryKey(['accessKey', 'customerId', 'eventNumber', 'eventType'], 'nfe_events_customer_id_access_key_event_number_event_type_pk'),
	)

	await db.createTable(
		'nfe_events_resumo',
		t => ({
			accessKey: t.name('access_key').text().index(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'nfe_events_resumo_customer_id_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.index(),
			date: t.timestampNoTZ().index(),
			nsu: t.integer().nullable(),
			eventProtocol: t.name('event_protocol').text(),
			eventType: t.name('event_type').integer(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfe_events_resumo_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
		}),
		t => t.primaryKey(['accessKey', 'customerId', 'eventProtocol'], 'nfe_events_resumo_customer_id_access_key_event_protocol_pk'),
	)

	await db.createTable(
		'nfe_resumo',
		t => ({
			accessKey: t.name('access_key').text(),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfe_resumo_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ(),
			json: t.json().nullable(),
			nsu: t.integer().nullable(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfe_resumo_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			cnpj: t.text(),
			name: t.text(),
			value: t.decimal(),
		}),
		t => t.primaryKey(['accessKey', 'customerId'], 'nfe_resumo_customer_id_access_key_pk'),
	)

	await db.createTable(
		'nfse',
		t => ({
			accessKey: t.name('access_key').varchar(50),
			customerId: t.name('customer_id').uuid().foreignKey('customers', 'id', {
				name: 'nfse_customer_id_customers_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ().index(),
			nsu: t.integer().nullable(),
			type: t.enum('public.nfse_type_enum').index(),
			sentToDominio: t.name('sent_to_dominio').boolean().default(t.sql`false`),
			prestadorCpfCnpj: t.name('prestador_cpf_cnpj').varchar(14),
			prestadorNome: t.name('prestador_nome').varchar(),
			tomadorCpfCnpj: t.name('tomador_cpf_cnpj').varchar(14).nullable(),
			tomadorNome: t.name('tomador_nome').varchar().nullable(),
			intermediarioCpfCnpj: t.name('intermediario_cpf_cnpj').varchar(14).nullable(),
			intermediarioNome: t.name('intermediario_nome').varchar().nullable(),
			value: t.decimal(13, 2),
			unconditionalDiscount: t.name('unconditional_discount').decimal(13, 2),
			iss: t.decimal(13, 2),
			issRetido: t.name('iss_retido').decimal(13, 2).default(t.sql`'0'::numeric`),
			irRetido: t.name('ir_retido').decimal(13, 2),
			csllRetido: t.name('csll_retido').decimal(13, 2),
			inssRetido: t.name('inss_retido').decimal(13, 2),
			description: t.text(),
			localServiceCode: t.name('local_service_code').varchar().nullable(),
			nacionalServiceCode: t.name('nacional_service_code').varchar().nullable(),
			cityCode: t
				.name('city_code')
				.integer()
				.foreignKey('cities', 'ibge_code', {
					name: 'nfse_city_code_cities_ibge_code_fk',
					onUpdate: 'CASCADE',
					onDelete: 'SET NULL',
				})
				.nullable(),
			conditionalDiscount: t.name('conditional_discount').decimal(13, 2),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfse_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			specialRegim: t.name('special_regim').integer().nullable(),
			number: t.varchar(),
			pisRetido: t.name('pis_retido').decimal(13, 2).default(t.sql`'0'::numeric`),
			cofinsRetido: t.name('cofins_retido').decimal(13, 2).default(t.sql`'0'::numeric`),
			otherDeductions: t.name('other_deductions').decimal(13, 2),
			isCanceled: t.name('is_canceled').enum('public.nfse_is_canceled_enum'),
			simplesNacional: t.name('simples_nacional').enum('public.nfse_simples_nacional_enum'),
			tomadorSimplesNacional: t.name('tomador_simples_nacional').enum('public.nfse_simples_nacional_enum'),
			tomadorMunicipio: t
				.name('tomador_municipio')
				.integer()
				.foreignKey('cities', 'ibge_code', {
					name: 'nfse_tomador_municipio_cities_ibge_code_fk',
					onUpdate: 'CASCADE',
					onDelete: 'SET NULL',
				})
				.nullable(),
			tomadorIsPessoaJuridica: t.name('tomador_is_pessoa_juridica').enum('public.nfse_tomador_is_pessoa_juridica_enum'),
			erpKey: t.name('erp_key').integer().nullable(),
			pdfUrl: t.name('pdf_url').text().nullable(),
			origin: t.enum('public.nfse_origin_enum'),
		}),
		t => t.primaryKey(['accessKey', 'customerId'], 'nfse_customer_id_access_key_pk'),
	)

	await db.createTable(
		'nfse_events',
		t => ({
			accessKey: t.name('access_key').text().index(),
			customerId: t
				.name('customer_id')
				.uuid()
				.foreignKey('customers', 'id', {
					name: 'nfse_events_customer_id_customers_id_fk',
					onUpdate: 'CASCADE',
					onDelete: 'CASCADE',
				})
				.index(),
			date: t.timestampNoTZ().index(),
			nsu: t.integer().nullable(),
			eventId: t.name('event_id').text(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'nfse_events_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			authorCnpj: t.name('author_cnpj').type('bpchar'),
			eventType: t.name('event_type').enum('public.nfse_events_types_enum'),
		}),
		t => t.primaryKey(['accessKey', 'customerId', 'eventId'], 'nfse_events_customer_id_access_key_event_id_pk'),
	)

	await db.createTable(
		'rents_payments',
		t => ({
			rentId: t.name('rent_id').uuid().foreignKey('rents', 'id', {
				name: 'rents_payments_rent_id_rents_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			date: t.timestampNoTZ(),
			fileId: t.name('file_id').uuid().foreignKey('files', 'id', {
				name: 'rents_payments_file_id_files_id_fk',
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			}),
			value: t.decimal(),
		}),
		t => t.primaryKey(['rentId', 'date'], 'rents_payments_rent_id_date_pk'),
	)
})
