import { getDocumentsFromPortalNacional } from '@/lib/portal-nacional-nfse/getDocumentsFromPortalNacional'
import { processPortalNacionalDocument } from '@/lib/portal-nacional-nfse/processPortalNacionalDocument'
import { db } from '@/src/db/db'
import { logger, schedules } from '@trigger.dev/sdk/v3'

export const captureNfsFromPortalNacionalTrigger = schedules.task({
	id: 'capture-nfs-from-portal-nacional',

	cron: '0 */12 * * *', // every 12 hours

	maxDuration: 3600 * 2, // 2 hours

	run: async () => {
		const today = new Date()

		const customers = await db.customers
			.select('id', 'title')
			.where({
				OR: [{ until: null }, { until: { gt: today } }],
				id: { not: '9e021097-26bc-4703-86aa-f19c0a350ef9' }, // Launch Pad
			})
			.where({
				id: { in: db.customersTaxNumbers.pluck('customerId').where({ type: { in: ['CNPJ', 'CPF'] } }) },
			})
			.where({
				id: {
					in: db.secrets.pluck('id').where({
						description: 'icp-brasil-certificate',
					}),
				},
			})
			.order('title')

		logger.log(`Iniciando captura de NF de ${customers.length} clientes.`)

		for (const { id, title } of customers) {
			const [documentsData, documentsError] = await getDocumentsFromPortalNacional({ customerId: id })

			if (!documentsData) {
				logger.error(`Erro ao capturar NF do cliente ${title} - id: ${id} - ${documentsError?.message}`)
				continue
			}

			for (const { nsu, documentType, xmlGzipped } of documentsData) {
				const [processData, processError] = await processPortalNacionalDocument({ customerId: id, nsu, documentType, xmlGzipped })

				if (!processData) {
					logger.error(`Erro ao processar NF do cliente ${title} - id: ${id} - ${processError?.message}`)
				}
			}

			logger.log(`Cliente ${title} - id: ${id} - ${documentsData.length} NF capturadas.`)
		}
	},
})
