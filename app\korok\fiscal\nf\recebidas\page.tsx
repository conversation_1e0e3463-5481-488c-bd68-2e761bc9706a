import { Table } from '@/components/Table/Table'
import { CopyButton } from '@/components/buttons/CopyButton'
import { CnpjButton } from '@/components/dataDisplay/CnpjButton'
import { MonetaryValue } from '@/components/dataDisplay/MonetaryValue'
import { dayjs } from '@/helpers/dayjs'
import { loadSearchParams } from '@/helpers/misc/loadSearchParams'
import { db } from '@/src/db/db'
import { ActionIcon } from '@mantine/core'
import { Group, Tooltip } from '@mantine/core'
import { Badge } from '@mantine/core'
import { IconExternalLink, IconTruckDelivery } from '@tabler/icons-react'
import {} from 'drizzle-orm'
import Link from 'next/link'
import type { SearchParams } from 'nuqs'
import { CaptureNfButton } from '../_components/CaptureNfButton'
import { DownloadAllXml } from '../_components/DownloadAllXml'
import { DownloadOneXml } from '../_components/DownloadOneXml'
import { GenerateEfdIcmsIpi } from './_components/GenerateEfdIcmsIpi'
import { SendEventButton } from './_components/SendEventButton'
import { getNfData } from './_data/getNfData'
import { columns } from './_helpers/columns'
import { title } from './_helpers/title'

type PageProps = {
	searchParams: Promise<SearchParams>
}

export default async function App({ searchParams }: PageProps) {
	const { from, until, customerId } = await loadSearchParams.load(searchParams)

	const [rawData] = await getNfData({ customerId, from, until })
	if (!rawData) return <></>

	const data = rawData.map(({ accessKey, date, value, isResumo, issuerCpfCnpj, issuerName: emitterName, number, isCanceled, alerts }) => ({
		id: accessKey,
		dataEmissao: date,
		emitente: {
			valueToExport: `${emitterName} ${issuerCpfCnpj}`,
			valueToFilter: `${emitterName} ${issuerCpfCnpj}`,
			valueToShow: (
				<Group wrap="nowrap">
					{emitterName}
					{issuerCpfCnpj && <CnpjButton value={issuerCpfCnpj} />}
				</Group>
			),
		},
		number: {
			valueToExport: number?.toString() ?? 0,
			valueToFilter: number?.toString() ?? 0,
			valueToShow: (
				<Group wrap="nowrap">
					{number}
					<CopyButton tooltip="Copiar chave de acesso" value={accessKey || ''} />
					<Tooltip label="Consultar no Portal Nacional">
						<Link href={`https://www.nfe.fazenda.gov.br/portal/consultaRecaptcha.aspx?nfe=${accessKey}`} prefetch={false} target="_blank">
							<ActionIcon variant="transparent">
								<IconExternalLink size={16} />
							</ActionIcon>
						</Link>
					</Tooltip>
				</Group>
			),
		},
		status: isResumo
			? {
					valueToExport: 'Apenas resumo',
					valueToFilter: 'Apenas resumo',
					valueToShow: <Badge color="yellow">Apenas resumo</Badge>,
				}
			: isCanceled
				? {
						valueToExport: 'Cancelada',
						valueToFilter: 'Cancelada',
						valueToShow: <Badge color="red">Cancelada</Badge>,
					}
				: {
						valueToExport: 'Autorizada',
						valueToFilter: 'Autorizada',
						valueToShow: <Badge color="green">Autorizada</Badge>,
					},
		value: {
			valueToExport: Number(value),
			valueToFilter: Number(value),
			valueToShow: <MonetaryValue value={Number(value)} />,
		},
		alerts: {
			valueToExport: alerts.length,
			valueToFilter: alerts.length,
			valueToShow:
				!isResumo && alerts.length ? (
					<Group>
						{alerts.map(p => (
							<Tooltip key={`${p.id}`} label={p.warning}>
								<Badge color="red">{p.warning}</Badge>
							</Tooltip>
						))}
					</Group>
				) : (
					<Badge color="green">TUDO CERTO!</Badge>
				),
		},
	}))

	const view = rawData.map(({ accessKey, isResumo }) => {
		if (isResumo) return undefined
		return { link: `/korok/fiscal/nf/recebidas/${accessKey}`, tooltip: 'Visualizar NF-e' }
	})

	const certificateData = await db.secrets
		.select({ expiration: 'expiration' })
		.where({
			id: customerId,
			description: 'icp-brasil-certificate',
		})
		.take()
	const certificateIsOk = certificateData?.expiration ? dayjs(certificateData.expiration).isAfter(dayjs()) : false

	const rowActions = rawData.map(({ isResumo, isCanceled, missingDestinationProducts, accessKey }) => {
		const searchParams = new URLSearchParams({ receber: 'true', from: from.toISOString(), until: until.toISOString(), customerId })

		return (
			<Group key={accessKey} wrap="nowrap">
				{isResumo && !isCanceled && <SendEventButton customerId={customerId} id={accessKey} certificateIsOk={!!certificateIsOk} />}

				{!isResumo && <DownloadOneXml accessKey={accessKey} />}

				{!isCanceled && !isResumo && (
					<Tooltip label={missingDestinationProducts ? 'Atribuir destinação' : 'Consultar destinação atribuída'}>
						<Link href={`/korok/fiscal/nf/recebidas/${accessKey}?${searchParams}`} prefetch={false}>
							<ActionIcon color={missingDestinationProducts ? 'red' : 'green'} data-testid="classify-button" variant="transparent">
								<IconTruckDelivery size={20} />
							</ActionIcon>
						</Link>
					</Tooltip>
				)}
			</Group>
		)
	})

	const onlyResumo = rawData.filter(e => e.isResumo)
	const onlyNotResumo = rawData.filter(e => !e.isResumo)

	const tools = [
		<SendEventButton customerId={customerId} id={onlyResumo.map(e => e.accessKey)} key={0} certificateIsOk={certificateIsOk} />,
		<DownloadAllXml customerId={customerId} disabled={!onlyNotResumo.length} from={from} key={1} type="Entrada" until={until} />,
		<CaptureNfButton customerId={customerId} key={2} certificateIsOk={certificateIsOk} />,
		<GenerateEfdIcmsIpi customerId={customerId} from={new Date(from)} key={3} until={new Date(until)} />,
	]

	return (
		<Table
			columns={columns}
			data={data}
			enablePagination={data.length > 200}
			rowActions={rowActions}
			title={title}
			tools={[tools]}
			view={view}
		/>
	)
}
