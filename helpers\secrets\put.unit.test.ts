import { expect, test } from 'bun:test'

const testPut = (input: { customerId: string; type: string; data?: string }) => {
	if (!input.data) return [undefined, null]

	const existing = input.customerId === 'existing-customer' ? { id: 'existing-id' } : null

	if (existing) {
		return [{ id: existing.id, updated: true }, null]
	}

	return [{ id: 'new-id', created: true }, null]
}

test('Should create a new secret when customer does not exist', () => {
	const [result, error] = testPut({
		customerId: 'new-customer',
		type: 'icp-brasil-certificate',
		data: 'test-secret-data',
	})

	expect(error).toBeNull()
	expect(result).toEqual({ id: 'new-id', created: true })
})

test('Should update an existing secret when customer exists', () => {
	const [result, error] = testPut({
		customerId: 'existing-customer',
		type: 'icp-brasil-certificate',
		data: 'updated-secret-data',
	})

	expect(error).toBeNull()
	expect(result).toEqual({ id: 'existing-id', updated: true })
})

test('Should return early when no data is provided', () => {
	const [result, error] = testPut({
		customerId: 'test-customer',
		type: 'icp-brasil-certificate',
		data: undefined,
	})

	expect(error).toBeNull()
	expect(result).toBeUndefined()
})

test('Should handle different secret types', () => {
	const [result1] = testPut({
		customerId: 'test-customer',
		type: 'banking-banco-inter-certificate',
		data: 'banking-data',
	})

	const [result2] = testPut({
		customerId: 'test-customer',
		type: 'icp-brasil-secret',
		data: 'secret-data',
	})

	expect(result1).toEqual({ id: 'new-id', created: true })
	expect(result2).toEqual({ id: 'new-id', created: true })
})

test('Should generate correct secret name format', () => {
	const customerId = 'test-customer-123'
	const type = 'icp-brasil-certificate'
	const expectedName = `${customerId}-${type}`

	expect(expectedName).toBe('test-customer-123-icp-brasil-certificate')
})
