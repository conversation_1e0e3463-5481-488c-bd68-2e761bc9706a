import { ca } from '@/db/ca'
import { orchidORM } from 'orchid-orm'
import { format } from 'sql-formatter'
import { highlight } from 'sql-highlight'
import { AccountsTable } from './tables/accounts.table'
import { AlvarasTable } from './tables/alvaras.table'
import { BankingBanksTable } from './tables/bankingBanks.table'
import { BankingBilletTable } from './tables/bankingBillet.table'
import { BankingPixTable } from './tables/bankingPix.table'
import { CertidoesTable } from './tables/certidoes.table'
import { CitiesTable } from './tables/cities.table'
import { ClassificationsTable } from './tables/classifications.table'
import { ClassificationsFlowsTasksTable } from './tables/classificationsFlowsTasks.table'
import { ClassificationsFlowsTaxesTable } from './tables/classificationsFlowsTaxes.table'
import { ClassificationsRulesTable } from './tables/classificationsRules.table'
import { ClerkUsersTable } from './tables/clerkUsers.table'
import { CnpjTable } from './tables/cnpj.table'
import { CnpjCnaesSecundariosTable } from './tables/cnpjCnaesSecundarios.table'
import { CnpjQsaTable } from './tables/cnpjQsa.table'
import { CteTable } from './tables/cte.table'
import { CteEventsTable } from './tables/cteEvents.table'
import { CteOsTable } from './tables/cteOs.table'
import { CustomBalanceSheetTable } from './tables/customBalanceSheet.table'
import { CustomersTable } from './tables/customers.table'
import { CustomersNfseSettingsTable } from './tables/customersNfseSettings.table'
import { CustomersPartnersTable } from './tables/customersPartners.table'
import { CustomersPayrollConfigsTable } from './tables/customersPayrollConfigs.table'
import { CustomersTaxNumbersTable } from './tables/customersTaxNumbers.table'
import { CustomersTaxPasswordsTable } from './tables/customersTaxPasswords.table'
import { CustomersTaxTimelineTable } from './tables/customersTaxTimeline.table'
import { CustomersTaxesConfigsTable } from './tables/customersTaxesConfigs.table'
import { DarfCodesTable } from './tables/darfCodes.table'
import { DocumentsTable } from './tables/documents.table'
import { DocumentsFilesTable } from './tables/documentsFiles.table'
import { EmailTable } from './tables/email.table'
import { EmailAttachmentsTable } from './tables/emailAttachments.table'
import { EmailMessagesRecipientsTable } from './tables/emailMessagesRecipients.table'
import { EmployeesTable } from './tables/employees.table'
import { FilePacksTable } from './tables/filePacks.table'
import { FilesTable } from './tables/files.table'
import { FilesTasksFlowTable } from './tables/filesTasksFlow.table'
import { FilesTaxesFlowTable } from './tables/filesTaxesFlow.table'
import { IeTable } from './tables/ie.table'
import { InstallmentsTable } from './tables/installments.table'
import { InvoicesTable } from './tables/invoices.table'
import { IrpfDocsTable } from './tables/irpfDocs.table'
import { JournalTable } from './tables/journal.table'
import { JustificativasTiposTable } from './tables/justificativasTipos.table'
import { MovementsTable } from './tables/movements.table'
import { NcmTable } from './tables/ncm.table'
import { NfeTable } from './tables/nfe.table'
import { NfeCteUltNsuTable } from './tables/nfeCteUltNsu.table'
import { NfeEntregaEventsTable } from './tables/nfeEntregaEvents.table'
import { NfeEventsTable } from './tables/nfeEvents.table'
import { NfeEventsResumoTable } from './tables/nfeEventsResumo.table'
import { NfeInvalidationTable } from './tables/nfeInvalidation.table'
import { NfeProductsTable } from './tables/nfeProducts.table'
import { NfeResumoTable } from './tables/nfeResumo.table'
import { NfseTable } from './tables/nfse.table'
import { NfseEventsTable } from './tables/nfseEvents.table'
import { OneOnOneTable } from './tables/oneOnOne.table'
import { OtherIncomesTable } from './tables/otherIncomes.table'
import { PayrollImporterTable } from './tables/payrollImporter.table'
import { PerfisTable } from './tables/perfis.table'
import { PermissionsTable } from './tables/permissions.table'
import { ProductsTable } from './tables/products.table'
import { RentsTable } from './tables/rents.table'
import { RentsHistoryTable } from './tables/rentsHistory.table'
import { RentsOwnersTable } from './tables/rentsOwners.table'
import { RentsPaymentsTable } from './tables/rentsPayments.table'
import { RentsRecipientsTable } from './tables/rentsRecipients.table'
import { RolesTable } from './tables/roles.table'
import { SecretsTable } from './tables/secrets.table'
import { StatesTable } from './tables/states.table'
import { TasksTable } from './tables/tasks.table'
import { TasksClassificationsTable } from './tables/tasksClassifications.table'
import { TasksRolesTable } from './tables/tasksRoles.table'
import { TaxDocumentsTable } from './tables/taxDocuments.table'
import { TaxPaymentsTable } from './tables/taxPayments.table'
import { TaxRulesTable } from './tables/taxRules.table'
import { TestimonyTable } from './tables/testimony.table'
import { UsersTable } from './tables/users.table'
import { XmlTypesTable } from './tables/xmlTypes.table'
import { XmlTypesConfigsTable } from './tables/xmlTypesConfigs.table'

const db = orchidORM(
	{
		ssl: { ca },
		databaseURL: process.env.DATABASE_URL,

		// define custom log (see https://orchid-orm.netlify.app/guide/orm-and-query-builder.html#log-option)
		log: {
			beforeQuery: () => performance.now(),

			afterQuery: ({ text, values }, start: number) => {
				const end = performance.now()
				const duration = (end - start) / 1000
				const color = duration >= 1 ? '\x1b[31m' : '\x1b[34m'
				const reset = '\x1b[0m'

				// add and element at the begining of the array (it should not be zero-based)
				if (Array.isArray(values) && values.length > 0) values.unshift('')

				const options = {
					language: 'postgresql' as const,
					keywordCase: 'lower' as const,
					params: values?.map(e => (e instanceof Date ? `'${e.toISOString()}'` : typeof e === 'number' ? String(e) : `'${e}'`)),
				}

				const formatted = format(text, options)
				const highlighted = highlight(formatted)

				console.log(`${color}------------------------------------ 🪑🎲  ------------------------------------${reset}`)
				console.log(`${highlighted}${reset}`)
				console.log(`${color}---------------------------- ⏳ ${duration}s ----------------------------${reset}`)
			},

			onError: (error, { text, values }) => {
				console.error(error)

				const color = '\x1b[31m'
				const reset = '\x1b[0m'

				// add and element at the begining of the array (it should not be zero-based)
				if (Array.isArray(values) && values.length > 0) values.unshift('')

				const options = {
					language: 'postgresql' as const,
					keywordCase: 'lower' as const,
					params: values?.map(e => (e instanceof Date ? `'${e.toISOString()}'` : typeof e === 'number' ? String(e) : `'${e}'`)),
				}

				const formatted = format(text, options)
				const highlighted = highlight(formatted)

				console.log(`${color}------------------------------------ ❌ ------------------------------------${reset}`)
				console.log(`${highlighted}${reset}`)
				console.log(`${color}------------------------------------ ❌ ------------------------------------${reset}`)
			},
		},
	},
	{
		accounts: AccountsTable,
		alvaras: AlvarasTable,
		bankingBanks: BankingBanksTable,
		bankingBillet: BankingBilletTable,
		bankingPix: BankingPixTable,
		certidoes: CertidoesTable,
		cities: CitiesTable,
		classifications: ClassificationsTable,
		classificationsFlowsTasks: ClassificationsFlowsTasksTable,
		classificationsFlowsTaxes: ClassificationsFlowsTaxesTable,
		classificationsRules: ClassificationsRulesTable,
		clerkUsers: ClerkUsersTable,
		cnpj: CnpjTable,
		cnpjCnaesSecundarios: CnpjCnaesSecundariosTable,
		cnpjQsa: CnpjQsaTable,
		cte: CteTable,
		cteEvents: CteEventsTable,
		cteOs: CteOsTable,
		customBalanceSheet: CustomBalanceSheetTable,
		customers: CustomersTable,
		customersNfseSettings: CustomersNfseSettingsTable,
		customersPartners: CustomersPartnersTable,
		customersPayrollConfigs: CustomersPayrollConfigsTable,
		secrets: SecretsTable,
		customersTaxNumbers: CustomersTaxNumbersTable,
		customersTaxPasswords: CustomersTaxPasswordsTable,
		customersTaxTimeline: CustomersTaxTimelineTable,
		customersTaxesConfigs: CustomersTaxesConfigsTable,
		darfCodes: DarfCodesTable,
		documents: DocumentsTable,
		documentsFiles: DocumentsFilesTable,
		email: EmailTable,
		emailAttachments: EmailAttachmentsTable,
		emailMessagesRecipients: EmailMessagesRecipientsTable,
		employees: EmployeesTable,
		filePacks: FilePacksTable,
		files: FilesTable,
		filesTasksFlow: FilesTasksFlowTable,
		filesTaxesFlow: FilesTaxesFlowTable,
		ie: IeTable,
		installments: InstallmentsTable,
		invoices: InvoicesTable,
		irpfDocs: IrpfDocsTable,
		journal: JournalTable,
		justificativasTipos: JustificativasTiposTable,
		movements: MovementsTable,
		ncm: NcmTable,
		nfe: NfeTable,
		nfeCteUltNsu: NfeCteUltNsuTable,
		nfeEntregaEvents: NfeEntregaEventsTable,
		nfeEvents: NfeEventsTable,
		nfeEventsResumo: NfeEventsResumoTable,
		nfeInvalidation: NfeInvalidationTable,
		nfeProducts: NfeProductsTable,
		nfeResumo: NfeResumoTable,
		nfse: NfseTable,
		nfseEvents: NfseEventsTable,
		oneOnOne: OneOnOneTable,
		otherIncomes: OtherIncomesTable,
		payrollImporter: PayrollImporterTable,
		perfis: PerfisTable,
		permissions: PermissionsTable,
		products: ProductsTable,
		rents: RentsTable,
		rentsHistory: RentsHistoryTable,
		rentsOwners: RentsOwnersTable,
		rentsPayments: RentsPaymentsTable,
		rentsRecipients: RentsRecipientsTable,
		roles: RolesTable,
		states: StatesTable,
		tasks: TasksTable,
		tasksClassifications: TasksClassificationsTable,
		tasksRoles: TasksRolesTable,
		taxDocuments: TaxDocumentsTable,
		taxPayments: TaxPaymentsTable,
		taxRules: TaxRulesTable,
		testimony: TestimonyTable,
		users: UsersTable,
		xmlTypes: XmlTypesTable,
		xmlTypesConfigs: XmlTypesConfigsTable,
	},
)

export { db }
