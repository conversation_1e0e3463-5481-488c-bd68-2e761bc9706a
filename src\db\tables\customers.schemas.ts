import { z } from 'zod'
import { CustomersTable } from './customers.table'
import { CustomersNfseSettingsInputSchema, CustomersNfseSettingsOutputSchema } from './customersNfseSettings.schemas'
import { CustomersPartnersInputSchema, CustomersPartnersOutputSchema } from './customersPartners.schemas'
import { CustomersPayrollConfigsInputSchema, CustomersPayrollConfigsOutputSchema } from './customersPayrollConfigs.schemas'
import { CustomersTaxNumbersInputSchema, CustomersTaxNumbersOutputSchema } from './customersTaxNumbers.schemas'
import { CustomersTaxPasswordsInputSchema, CustomersTaxPasswordsOutputSchema } from './customersTaxPasswords.schemas'
import { CustomersTaxTimelineInputSchema, CustomersTaxTimelineOutputSchema } from './customersTaxTimeline.schemas'
import { CustomersTaxesConfigsInputSchema, CustomersTaxesConfigsOutputSchema } from './customersTaxesConfigs.schemas'
import { SecretsInputSchema, SecretsOutputSchema } from './secrets.schemas'

export const CustomersInputSchema = CustomersTable.inputSchema().extend({
	nfseSettings: z.array(CustomersNfseSettingsInputSchema),
	partners: z.array(CustomersPartnersInputSchema),
	payrollConfigs: z.array(CustomersPayrollConfigsInputSchema),
	taxesConfigs: z.array(CustomersTaxesConfigsInputSchema),
	taxNumbers: z.array(CustomersTaxNumbersInputSchema),
	taxPasswords: z.array(CustomersTaxPasswordsInputSchema),
	taxTimelines: z.array(CustomersTaxTimelineInputSchema),
	secrets: z.array(SecretsInputSchema),
})

export const CustomersOutputSchema = CustomersTable.outputSchema().extend({
	nfseSettings: z.array(CustomersNfseSettingsOutputSchema),
	partners: z.array(CustomersPartnersOutputSchema),
	payrollConfigs: z.array(CustomersPayrollConfigsOutputSchema),
	taxesConfigs: z.array(CustomersTaxesConfigsOutputSchema),
	taxNumbers: z.array(CustomersTaxNumbersOutputSchema),
	taxPasswords: z.array(CustomersTaxPasswordsOutputSchema),
	taxTimelines: z.array(CustomersTaxTimelineOutputSchema),
	secrets: z.array(SecretsOutputSchema),
})
