import { captureNfFromSefaz } from '@/lib/sefaz-nf/captureNfFromSefaz'
import { db } from '@/src/db/db'
import { logger, schedules } from '@trigger.dev/sdk/v3'

export const captureNfFromSefazTrigger = schedules.task({
	id: 'capture-nf-from-sefaz',

	machine: 'medium-1x',

	cron: '10 */1 * * *', // every 1 hour and 10 minutes

	maxDuration: 3600 * 1, // 1 hours

	run: async () => {
		const today = new Date()

		const customers = await db.customers
			.select('id', 'title')
			.where({
				OR: [{ until: null }, { until: { gt: today } }],
				id: { not: '9e021097-26bc-4703-86aa-f19c0a350ef9' }, // Launch Pad
			})
			.where({
				id: { in: db.customersTaxNumbers.pluck('customerId').where({ type: { in: ['CNPJ', 'CPF'] } }) },
			})
			.where({
				id: {
					in: db.secrets.pluck('id').where({
						description: 'icp-brasil-certificate',
					}),
				},
			})
			.order('title')

		logger.log(`Iniciando captura de NF de ${customers.length} clientes.`)

		for (const { id, title } of customers) {
			const [captured, error] = await captureNfFromSefaz({ customerId: id })

			if (error) {
				logger.error(`Erro ao capturar NF do cliente ${title} - id: ${id} - ${error}`)
				continue
			}

			logger.log(`Cliente ${title} - id: ${id} - ${captured} notas capturadas.`)
		}
	},
})
