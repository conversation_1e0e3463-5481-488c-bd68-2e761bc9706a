import 'server-only'

import { captureCtFromSefaz } from '@/lib/sefaz-ct/captureCtFromSefaz'
import { db } from '@/src/db/db'
import { logger, schedules } from '@trigger.dev/sdk/v3'

export const captureCtFromSefazTrigger = schedules.task({
	id: 'capture-ct-from-sefaz',

	machine: 'medium-1x',

	cron: '0 * * * *', // every hour

	maxDuration: 3600 * 3, // 3 hours

	run: async () => {
		const today = new Date()

		const customers = await db.customers
			.select('id', 'title')
			.where({
				OR: [{ until: null }, { until: { gt: today } }],
				id: { not: '9e021097-26bc-4703-86aa-f19c0a350ef9' }, // Launch Pad
			})
			.where({
				id: { in: db.customersTaxNumbers.pluck('customerId').where({ type: { in: ['CNPJ', 'CPF'] } }) },
			})
			.where({
				id: {
					in: db.secrets.pluck('id').where({
						description: 'icp-brasil-certificate',
					}),
				},
			})
			.order('title')

		logger.log(`Iniciando captura de CT de ${customers.length} clientes.`)

		for (const { id, title } of customers) {
			const [captured, error] = await captureCtFromSefaz({ customerId: id })

			if (error) {
				logger.error(`Erro ao capturar CT do cliente ${title} - id: ${id} - ${error}`)
				continue
			}

			logger.log(`Cliente ${title} - id: ${id} - ${captured}`)
		}
	},
})
