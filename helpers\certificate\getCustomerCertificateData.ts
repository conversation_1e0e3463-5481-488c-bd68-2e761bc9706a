import 'server-only'

import { action } from '@/lib/actions/action'
import { db } from '@/src/db/db'
import * as forge from 'node-forge'
import { z } from 'zod'

export const getCustomerCertificateData = action
	.input(z.object({ customerId: z.string().uuid() }))
	.output(z.object({ certificateData: z.instanceof(Buffer), passphraseData: z.instanceof(Buffer) }))
	.handler(async ({ input: { customerId } }) => {
		const secrets = await db.secrets.select('type', 'expiration', 'data').where({
			customerId,
			type: { in: ['icp-brasil-certificate', 'icp-brasil-secret'] },
		})

		const cert = secrets.find(s => s.type === 'icp-brasil-certificate')
		const pass = secrets.find(s => s.type === 'icp-brasil-secret')

		if (!cert || !pass) {
			throw new Error(
				'O certificado digital deste cliente não foi encontrado. Verifique se o certificado está cadastrado e dentro do período de validade',
				{ cause: 'expected' },
			)
		}

		if (!cert.expiration || new Date(cert.expiration) < new Date()) {
			throw new Error('O certificado digital deste cliente está expirado. Verifique se há um certificado válido cadastrado', {
				cause: 'expected',
			})
		}

		if (!cert.data || !pass.data) throw new Error('Certificate data not found')

		const certBuffer = Buffer.from(cert.data, 'base64')
		const passBuffer = Buffer.from(pass.data)

		const p12Der = forge.util.createBuffer(certBuffer.toString('binary'))
		const p12 = forge.pkcs12.pkcs12FromAsn1(forge.asn1.fromDer(p12Der), pass.data)

		const certOid = forge.pki.oids.certBag as string
		const keyOid = forge.pki.oids.pkcs8ShroudedKeyBag as string

		if (!certOid || !keyOid) throw new Error('OID missing')

		const certBags = (p12.getBags({ bagType: certOid }) as Record<string, { cert: forge.pki.Certificate }[]>)[certOid]
		const keyBags = (p12.getBags({ bagType: keyOid }) as Record<string, { key: forge.pki.PrivateKey }[]>)[keyOid]

		if (!certBags || !keyBags || !keyBags[0]) throw new Error('Certificate or key not found')

		const keyPem = forge.pki.privateKeyToPem(keyBags[0].key)
		const certObjects = certBags.map(bag => forge.pki.certificateFromPem(forge.pki.certificateToPem(bag.cert)))

		const newP12Asn1 = forge.pkcs12.toPkcs12Asn1(forge.pki.privateKeyFromPem(keyPem), certObjects, pass.data, {
			friendlyName: 'myCert',
			generateLocalKeyId: true,
		})
		const newP12Buffer = Buffer.from(forge.asn1.toDer(newP12Asn1).getBytes(), 'binary')

		return { certificateData: newP12Buffer, passphraseData: passBuffer }
	})
