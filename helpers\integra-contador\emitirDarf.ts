'use server'

import old from '@/db/db'
import { cities } from '@/db/schema/cities'
import { customersTaxNumbers } from '@/db/schema/customersTaxNumbers'
import { darfCodes } from '@/db/schema/darfCodes'
import { states } from '@/db/schema/states'
import { TaxDocumentsSchema } from '@/db/schema/taxDocuments'
import { customersWithCnpj } from '@/db/schema/views/customersWithCnpj'
import { fetchWithSignedCertificate } from '@/helpers/certificate/fetchWithSignedCertificate'
import { getIntegraContadorToken } from '@/helpers/integra-contador/getIntegraContadorToken'
import { korokConfig } from '@/korok.config'
import { action } from '@/lib/actions/action'
import { getAccountantCustomerId } from '@/lib/efd-reinf/getAccountantCustomerId'
import { db } from '@/src/db/db'
import { isCNPJ } from 'brazilian-values'
import dayjs from 'dayjs'
import { and, eq } from 'drizzle-orm'
import { z } from 'zod'

const fallbackCnpj = korokConfig.tenantContabilidade.cnpj

const URL = 'https://gateway.apiserpro.serpro.gov.br/integra-contador/v1/Emitir'

const schema = z.object({
	contratante: z.object({ numero: z.string(), tipo: z.number() }),
	autorPedidoDados: z.object({ numero: z.string(), tipo: z.number() }),
	contribuinte: z.object({ numero: z.string(), tipo: z.number() }),
	pedidoDados: z.object({
		idSistema: z.literal('SICALC'),
		idServico: z.literal('CONSOLIDARGERARDARF51'),
		versaoSistema: z.literal('2.9'),
		dados: z.string(),
	}),
	status: z.number(),
	responseId: z.string(),
	dados: z
		.preprocess(
			e => (typeof e === 'string' ? JSON.parse(e) : e),
			z.object({
				consolidado: z.object({
					valorPrincipalMoedaCorrente: z.number(),
					valorTotalConsolidado: z.number(),
					valorMultaMora: z.number(),
					percentualMultaMora: z.number(),
					valorJuros: z.number(),
					percentualJuros: z.number(),
					termoInicialJuros: z.coerce.date(),
					dataArrecadacaoConsolidacao: z.coerce.date(),
					dataValidadeCalculo: z.coerce.date(),
				}),
				darf: z.string(),
			}),
		)
		.nullable(),
	mensagens: z.array(z.object({ codigo: z.string(), texto: z.string() })),
})

export const emitirDarf = action

	.input(
		TaxDocumentsSchema.pick({
			customerId: true,
			cityId: true,
			darfCodeId: true,
			dueDate: true,
			value: true,
			fine: true,
			interest: true,
			created: true,
			quota: true,
			details: true,
		}).extend({
			referencePeriod: z.string(),
		}),
	)

	.output(schema)

	.handler(
		async ({ input: { customerId, cityId, darfCodeId, referencePeriod, dueDate, value, fine, interest, created, quota, details } }) => {
			if (!process.env.INTEGRA_CONTADOR_API_KEY) throw new Error('INTEGRA_CONTADOR_API_KEY not found in environment variables')
			if (!darfCodeId) throw new Error('Não foi possível encontrar o código do DARF')

			const certificateData = await db.secrets
				.select({ expiration: 'expiration' })
				.where({ id: customerId, description: 'icp-brasil-certificate' })
				.take()

			const certificateIsOk = certificateData?.expiration ? dayjs(certificateData.expiration).isAfter(dayjs()) : false

			const certificateCustomerId = certificateIsOk
				? customerId
				: await old
						.select({ customerId: customersTaxNumbers.customerId })
						.from(customersTaxNumbers)
						.where(and(eq(customersTaxNumbers.type, 'CNPJ'), eq(customersTaxNumbers.taxNumber, fallbackCnpj)))
						.limit(1)
						.then(e => e[0]?.customerId)
			if (!certificateCustomerId) throw new Error('Não foi possível encontrar o CNPJ do certificado digital do cliente')

			const [headers, headersError] = await getIntegraContadorToken()
			if (!headers) throw new Error('Não foi possível obter o token do Integra Contador', { cause: headersError })

			const [cityData] = await old
				.select({
					rfbCode: cities.rfbCode,
					stateabbreviation: states.abbreviation,
				})
				.from(cities)
				.innerJoin(states, eq(cities.stateId, states.id))
				.where(eq(cities.id, cityId))
			if (!cityData) throw new Error('Não foi possível obter os dados do município deste cliente.')

			const customerDataPromise = old
				.select({ cnpj: customersWithCnpj.cnpj })
				.from(customersWithCnpj)
				.where(eq(customersWithCnpj.customerId, customerId))

			const darfCodeDataPromise = await old
				.select({ code: darfCodes.code, pAType: darfCodes.pAType })
				.from(darfCodes)
				.where(eq(darfCodes.id, darfCodeId))

			const [[customerData], [darfCodeData], [accountantCustomerId]] = await Promise.all([
				customerDataPromise,
				darfCodeDataPromise,
				getAccountantCustomerId(),
			])
			if (!customerData) throw new Error('Não foi possível obter os dados do cliente deste cliente.')
			if (!darfCodeData) throw new Error('Não foi possível obter os dados do código do DARF deste cliente.')
			if (!accountantCustomerId) throw new Error('Accountant customer ID not found')

			const urlencoded = new URLSearchParams()
			urlencoded.append('grant_type', 'client_credentials')

			const requestOptions = {
				method: 'POST',
				headers: headers,
				body: JSON.stringify({
					contratante: { numero: fallbackCnpj, tipo: 2 },
					autorPedidoDados: { numero: fallbackCnpj, tipo: 2 },
					contribuinte: { numero: customerData.cnpj, tipo: isCNPJ(customerData.cnpj) ? 2 : 1 },
					pedidoDados: {
						idSistema: 'SICALC',
						idServico: 'CONSOLIDARGERARDARF51',
						versaoSistema: '2.9',
						dados: JSON.stringify({
							uf: cityData.stateabbreviation,
							municipio: cityData.rfbCode.toString(),
							codigoReceita: darfCodeData.code.slice(0, 4),
							codigoReceitaExtensao: darfCodeData.code.slice(-2),
							tipoPA: darfCodeData.pAType,
							dataPA: referencePeriod,
							cota: quota?.toString(),
							valorImposto: Number(value).toFixed(2),
							valorMulta: Number(fine).toFixed(2),
							valorJuros: Number(interest).toFixed(2),
							dataConsolidacao: created.toISOString(),
							observacao: details,
							vencimento: dueDate.toISOString(),
						}),
					},
				}),
			}

			const [req, error] = await fetchWithSignedCertificate({
				customerId,
				url: URL,
				requestOptions,
				accountantCustomerId: accountantCustomerId,
				useAccountantCertificate: 'always',
			})
			if (!req) throw error

			const data = schema.parse(await req.json())

			if (!data?.dados) throw new Error(data.mensagens.map(e => e.texto).join('\n'))

			const pdfBytes = Uint8Array.from(atob(data?.dados.darf), c => c.charCodeAt(0))
			const pdfBlob = new Blob([pdfBytes], { type: 'application/pdf' })
			const dataBase64 = Buffer.from(await pdfBlob.arrayBuffer()).toString('base64')

			await db.files
				.create({
					customerId,
					data: dataBase64,
					name: `${customerData.cnpj}-${referencePeriod}-${darfCodeData.code.slice(0, 4)}-${darfCodeData.code.slice(-2)}.pdf`,
					type: 'pdf',
				})
				.onConflict('hash')
				.merge()

			return data
		},
	)
