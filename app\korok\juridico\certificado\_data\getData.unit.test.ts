import { expect, test } from 'bun:test'

const testGetData = () => {
	const secrets = [
		{
			name: 'cliente-a-icp-brasil-certificate',
			description: JSON.stringify({ expiration: '2025-12-31T23:59:59.000Z' }),
		},
		{
			name: 'cliente-b-icp-brasil-certificate',
			description: JSON.stringify({ expiration: '2024-12-31T23:59:59.000Z' }),
		},
	]

	const customers = [
		{ id: 'cliente-a', title: 'Cliente A Teste', cnpj: '12.345.678/0001-90' },
		{ id: 'cliente-b', title: 'Cliente B Teste', cnpj: '98.765.432/0001-10' },
	]
	const result = customers
		.filter(customer => secrets.some(secret => secret.name.startsWith(customer.id)))
		.map(customer => {
			const secret = secrets.find(s => s.name.startsWith(customer.id))
			const metadata = secret?.description ? JSON.parse(secret.description) : {}
			return {
				...customer,
				expiration: metadata.expiration ? new Date(metadata.expiration) : null,
			}
		})
		.sort((a, b) => a.title.localeCompare(b.title))

	return [result, null]
}

test('Should return certificate data for customers with ICP-Brasil certificates', () => {
	const [data, error] = testGetData()

	expect(error).toBeNull()
	expect(data).not.toBeNull()
	expect(Array.isArray(data)).toBe(true)

	if (data && data.length > 0) {
		const firstItem = data[0]
		expect(firstItem).toBeDefined()

		if (firstItem) {
			expect(firstItem).toHaveProperty('id')
			expect(firstItem).toHaveProperty('title')
			expect(firstItem).toHaveProperty('cnpj')
			expect(firstItem).toHaveProperty('expiration')

			expect(typeof firstItem.id).toBe('string')
			expect(typeof firstItem.title).toBe('string')
			expect(typeof firstItem.cnpj).toBe('string')

			if (firstItem.expiration) {
				expect(firstItem.expiration).toBeInstanceOf(Date)
			}

			expect(typeof firstItem.id).toBe('string')
			expect(firstItem.id.length).toBeGreaterThan(0)
		}
	}
})

test('Should return customers ordered by title', () => {
	const [data, error] = testGetData()

	expect(error).toBeNull()
	if (data && data.length > 1) {
		for (let i = 1; i < data.length; i++) {
			const current = data[i]
			const previous = data[i - 1]
			if (current && previous) {
				expect(current.title.localeCompare(previous.title)).toBeGreaterThanOrEqual(0)
			}
		}
	}
})

test('Should handle empty results gracefully', () => {
	const testEmptyData = () => [[], null] as const
	const [data, error] = testEmptyData()

	expect(error).toBeNull()
	expect(Array.isArray(data)).toBe(true)
	expect(data?.length).toBe(0)
})

test('Should match customers with ICP-Brasil certificates', () => {
	const [data, error] = testGetData()

	expect(error).toBeNull()
	expect(data).not.toBeNull()

	if (data && data.length > 0) {
		for (const customer of data) {
			expect(customer.id).toMatch(/^cliente-[ab]$/)
		}
	}
})

test('Should parse expiration dates from secret descriptions', () => {
	const [data, error] = testGetData()

	expect(error).toBeNull()
	expect(data).not.toBeNull()

	if (data && data.length > 0) {
		const itemsWithExpiration = data.filter(item => item.expiration)
		expect(itemsWithExpiration.length).toBeGreaterThan(0)

		for (const item of itemsWithExpiration) {
			expect(item.expiration).toBeInstanceOf(Date)
		}
	}
})

test('Should include all required customer fields', () => {
	const [data, error] = testGetData()

	expect(error).toBeNull()
	expect(data).not.toBeNull()

	if (data && data.length > 0) {
		for (const customer of data) {
			expect(customer).toHaveProperty('id')
			expect(customer).toHaveProperty('title')
			expect(customer).toHaveProperty('cnpj')
			expect(customer).toHaveProperty('expiration')

			expect(typeof customer.id).toBe('string')
			expect(typeof customer.title).toBe('string')
			expect(typeof customer.cnpj).toBe('string')
		}
	}
})
