import { Table } from '@/components/Table/Table'
import { CopyButton } from '@/components/buttons/CopyButton'
import { CnpjButton } from '@/components/dataDisplay/CnpjButton'
import { MonetaryValue } from '@/components/dataDisplay/MonetaryValue'
import { dayjs } from '@/helpers/dayjs'
import { loadSearchParams } from '@/helpers/misc/loadSearchParams'
import { db } from '@/src/db/db'
import { ActionIcon, Text } from '@mantine/core'
import { Group, Tooltip } from '@mantine/core'
import { Badge } from '@mantine/core'
import { IconExternalLink } from '@tabler/icons-react'
import { formatToCNPJ } from 'brazilian-values'
import {} from 'drizzle-orm'
import Link from 'next/link'
import type { SearchParams } from 'nuqs'
import { CaptureNfButton } from '../_components/CaptureNfButton'
import { DownloadAllXml } from '../_components/DownloadAllXml'
import { DownloadOneXml } from '../_components/DownloadOneXml'
import { getNfData } from './_data/getNfData'
import { columns } from './_helpers/columns'
import { title } from './_helpers/title'

type PageProps = {
	params: Promise<{ id: string }>
	searchParams: Promise<SearchParams>
}

export default async function App({ searchParams }: PageProps) {
	const { from, until, customerId } = await loadSearchParams.load(searchParams)

	const [rawData] = await getNfData({ customerId, from, until })
	if (!rawData) return <></>

	const data = rawData.map(({ alerts, accessKey, isCanceled, issuerUf, receiverName, receiverCpfCnpj, number, date, value }) => ({
		id: accessKey,
		dataEmissao: date,
		destinatario: {
			valueToExport: receiverCpfCnpj ? `${receiverName} ${formatToCNPJ(receiverCpfCnpj)}` : '',
			valueToFilter: receiverCpfCnpj ? `${receiverName} ${formatToCNPJ(receiverCpfCnpj)}` : '',
			valueToShow: (
				<Group wrap="nowrap">
					{receiverName}
					<CnpjButton value={receiverCpfCnpj ?? ''} />
					<Badge>{issuerUf}</Badge>
				</Group>
			),
		},
		number: {
			valueToExport: number?.toString() ?? '',
			valueToFilter: number?.toString() ?? '',
			valueToShow: (
				<Group wrap="nowrap">
					<Text>{number}</Text>
					<CopyButton tooltip="Copiar chave de acesso" value={accessKey || ''} />
					<Tooltip label="Consultar no Portal Nacional">
						<Link href={`https://www.nfe.fazenda.gov.br/portal/consultaRecaptcha.aspx?nfe=${accessKey}`} prefetch={false} target="_blank">
							<ActionIcon variant="transparent">
								<IconExternalLink size={16} />
							</ActionIcon>
						</Link>
					</Tooltip>
				</Group>
			),
		},
		status: isCanceled
			? {
					valueToExport: 'Cancelada',
					valueToFilter: 'Cancelada',
					valueToShow: <Badge color="red">Cancelada</Badge>,
				}
			: {
					valueToExport: 'Autorizada',
					valueToFilter: 'Autorizada',
					valueToShow: <Badge color="green">Autorizada</Badge>,
				},
		value: {
			valueToExport: value ?? 0,
			valueToFilter: value ?? 0,
			valueToShow: <MonetaryValue value={Number(value ?? 0)} />,
		},
		alerts: {
			valueToExport: alerts.length,
			valueToFilter: alerts.length,
			valueToShow: alerts.length ? (
				<Group>
					{alerts.map(p => (
						<Tooltip key={`${p.id}`} label={p.warning}>
							<Badge color="red">{p.warning}</Badge>
						</Tooltip>
					))}
				</Group>
			) : (
				<Badge color="green">TUDO CERTO!</Badge>
			),
		},
	}))

	const view = rawData.map(({ accessKey, receiverName }) => {
		if (!receiverName) return undefined // If there is no Receiver Name, it means it is a NFC-e

		return {
			link: `/korok/fiscal/nf/emitidas/${accessKey}?${new URLSearchParams({ from: from.toISOString(), until: until.toISOString(), customerId })}`,
			tooltip: 'Visualizar NF-e',
		}
	})

	const rowActions = rawData.map(({ isCanceled, accessKey }) =>
		!isCanceled ? <DownloadOneXml accessKey={accessKey} key={accessKey} /> : null,
	)

	const certificateData = await db.secrets
		.select({ expiration: 'expiration' })
		.where({
			id: customerId,
			description: 'icp-brasil-certificate',
		})
		.take()

	const certificateIsOk = certificateData?.expiration ? dayjs(certificateData.expiration).isAfter(dayjs()) : false

	const tools = [
		<DownloadAllXml key="tools-0" customerId={customerId} from={from} until={until} disabled={!rawData.length} type="Saída" />,
		<CaptureNfButton customerId={customerId} key={2} certificateIsOk={certificateIsOk} />,
	]

	return (
		<Table
			columns={columns}
			data={data}
			enablePagination={data.length > 200}
			rowActions={rowActions}
			title={title}
			tools={[tools]}
			view={view}
		/>
	)
}
