import { action } from '@/lib/actions/action'
import { db } from '@/src/db/db'
import { z } from 'zod'

export const getBankIntegrationData = action

	.input(z.object({ customerId: z.string() }))

	.output(z.object({ isEnabled: z.boolean() }))

	.handler(async ({ input: { customerId } }) => {
		const data = await db.secrets.select({ id: 'id' }).where({
			id: customerId,
			description: 'banking-banco-inter-certificate',
		})

		return { isEnabled: !!data.length }
	})
