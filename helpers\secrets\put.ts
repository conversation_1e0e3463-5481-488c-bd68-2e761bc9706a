import 'server-only'

import { action } from '@/lib/actions/action'
import { getSupabaseAdmin } from '@/lib/supabase/admin-client'
import { z } from 'zod'

export const put = action
	.input(
		z.object({
			customerId: z.string().uuid().optional(),
			type: z.string(),
			data: z.string().optional(),
			secretId: z.string().uuid().optional(),
		}),
	)
	.handler(async ({ input: { customerId, data, type, secretId } }) => {
		if (!data) return

		const supabase = getSupabaseAdmin()
		const name = type

		// Calculate expiration for certificates
		let expiration: Date | null = null
		if (type === 'icp-brasil-certificate' && customerId) {
			try {
				const { calculateCertificateExpiration } = await import(
					'@/app/korok/controles/clientes/[id]/_helpers/calculateCertificateExpiration'
				)

				// Try to get the passphrase from existing secrets
				const { data: existingPassphrase } = await supabase
					.from('vault.decrypted_secrets')
					.select('secret')
					.eq('customer_id', customerId)
					.eq('name', 'icp-brasil-secret')
					.maybeSingle()

				if (existingPassphrase?.secret) {
					expiration = calculateCertificateExpiration({
						certificateData: data,
						passphraseData: existingPassphrase.secret,
					})
				}
			} catch (error) {
				console.warn('Failed to calculate certificate expiration:', error)
			}
		}

		// Create metadata object with expiration if calculated
		const metadata = expiration ? { expiration: expiration.toISOString() } : {}
		const description = Object.keys(metadata).length > 0 ? JSON.stringify(metadata) : type

		// Check if secret exists by secretId (for updates)
		let existing = null
		if (secretId) {
			const { data: existingSecret, error: checkError } = await supabase
				.from('vault.decrypted_secrets')
				.select('id')
				.eq('id', secretId)
				.maybeSingle()

			if (checkError) throw new Error(`Failed to check existing secret: ${checkError.message}`)
			existing = existingSecret
		}

		const rpcCall = existing
			? supabase.rpc('vault_update_secret', { secret_id: secretId, secret: data, name, description })
			: supabase.rpc('vault_create_secret', { secret: data, name, description })

		const { data: result, error } = await rpcCall
		if (error) throw new Error(`Failed to ${existing ? 'update' : 'create'} secret: ${error.message}`)

		// Return the secret ID (either existing or newly created)
		return { secretId: existing ? secretId : result, customerId }
	})
