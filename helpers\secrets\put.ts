import 'server-only'

import { action } from '@/lib/actions/action'
import { getSupabaseAdmin } from '@/lib/supabase/admin-client'
import { z } from 'zod'

export const put = action
	.input(
		z.object({
			customerId: z.string().uuid(),
			type: z.string(),
			data: z.string().optional(),
		}),
	)
	.handler(async ({ input: { customerId, data, type } }) => {
		if (!data) return

		const supabase = getSupabaseAdmin()
		const name = `${customerId}-${type}`

		// Calculate expiration for certificates
		let expiration: Date | null = null
		if (type === 'icp-brasil-certificate') {
			try {
				const { calculateCertificateExpiration } = await import(
					'@/app/korok/controles/clientes/[id]/_helpers/calculateCertificateExpiration'
				)

				// Try to get the passphrase from existing secrets
				const { data: existingPassphrase } = await supabase
					.from('vault.decrypted_secrets')
					.select('secret')
					.eq('id', customerId)
					.eq('name', `${customerId}-icp-brasil-secret`)
					.maybeSingle()

				if (existingPassphrase?.secret) {
					expiration = calculateCertificateExpiration({
						certificateData: data,
						passphraseData: existingPassphrase.secret,
					})
				}
			} catch (error) {
				console.warn('Failed to calculate certificate expiration:', error)
			}
		}

		// Create metadata object with expiration if calculated
		const metadata = expiration ? { expiration: expiration.toISOString() } : {}
		const description = Object.keys(metadata).length > 0 ? JSON.stringify(metadata) : type

		const { data: existing, error: checkError } = await supabase
			.from('vault.decrypted_secrets')
			.select('id')
			.eq('id', customerId)
			.eq('name', name)
			.maybeSingle()

		if (checkError) throw new Error(`Failed to check existing secret: ${checkError.message}`)

		const rpcCall = existing
			? supabase.rpc('vault_update_secret', { secret_id: customerId, secret: data, name, description })
			: supabase.rpc('vault_create_secret', { secret_id: customerId, secret: data, name, description })

		const { data: result, error } = await rpcCall
		if (error) throw new Error(`Failed to ${existing ? 'update' : 'create'} secret: ${error.message}`)
		return result
	})
