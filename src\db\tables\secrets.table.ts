import type { Insertable, Selectable, Updatable } from 'orchid-orm'
import { BaseTable } from '../baseTable'
import { CustomersTable } from './customers.table'

export type Secrets = Selectable<SecretsTable>
export type SecretsNew = Insertable<SecretsTable>
export type SecretsUpdate = Updatable<SecretsTable>

export class SecretsTable extends BaseTable {
	readonly table = 'vault.secrets'

	columns = this.setColumns(t => ({
		id: t.uuid().primaryKey(),
		name: t.text(),
		description: t.text().nullable(),
		secret: t.text(),
		keyId: t.name('key_id').uuid(),
		nonce: t.text(),
		createdAt: t.name('created_at').timestampNoTZ(),
		updatedAt: t.name('updated_at').timestampNoTZ(),
	}))

	computed = this.setComputed(({ computeBatchAtRuntime }) => ({
		type: computeBatchAtRuntime(['description'], async secrets => secrets.map(({ description }) => description || '')),
		expiration: computeBatchAtRuntime(['description'], async secrets =>
			secrets.map(({ description }) => {
				try {
					const metadata = JSON.parse(description || '{}')
					return metadata.expiration ? new Date(metadata.expiration) : null
				} catch {
					return null
				}
			}),
		),
		data: computeBatchAtRuntime(['secret'], async secrets =>
			secrets.map(({ secret }) => secret?.replaceAll('data:application/x-pkcs12;base64,', '') || ''),
		),
	}))

	relations = {
		customer: this.hasOne(() => CustomersTable, { columns: ['id'], references: ['id'] }),
	}
}
