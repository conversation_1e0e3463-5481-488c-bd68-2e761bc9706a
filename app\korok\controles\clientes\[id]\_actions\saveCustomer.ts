'use server'

import { action } from '@/lib/actions/action'
import { disableCustomersOnInvoicy } from '@/lib/invoicy/disableCustomersOnInvoicy'
import { saveCustomersOnInvoicy } from '@/lib/invoicy/saveCustomersOnInvoicy'
import { saveCustomersOnRocketChat } from '@/lib/rocketchat/saveCustomersOnRocketChat'
import { saveCustomersOnSeafile } from '@/lib/seafile/saveCustomersOnSeafile'
import { db } from '@/src/db/db'
import { CustomersInputSchema } from '@/src/db/tables/customers.schemas'

export const saveCustomer = action

	.input(CustomersInputSchema)

	.handler(async ({ input }) => {
		const { nfseSettings, secrets, taxesConfigs, taxPasswords, taxTimelines, partners, payrollConfigs, taxNumbers, ...customerData } = input

		let customerId = customerData.id

		// Upload secrets first to get vault-generated IDs
		const { put } = await import('@/helpers/secrets/put')
		const savedSecrets = []

		for (const { type, data } of secrets) {
			const [savedSecret, savedSecretError] = await put({ type, data })
			if (!savedSecret) throw savedSecretError
			savedSecrets.push({ ...savedSecret, type })
		}

		// If this is a new customer (no existing ID), use the first secret ID as customer ID
		if (!customerId && savedSecrets.length > 0) {
			customerId = savedSecrets[0].secretId
			customerData.id = customerId
		}

		// create main record
		await db.customers.create(customerData).onConflict('id').merge()

		// Prepare secrets data with proper customerId and secretId mapping
		const secretsWithCustomerId = savedSecrets.map(({ secretId, type }) => ({
			id: secretId,
			customerId,
			type,
		}))

		// create related records
		const result = await db.customers
			.find(customerId)
			.update({
				taxNumbers: { delete: { customerId } },
				nfseSettings: { delete: { customerId } },
				partners: { delete: { customerId } },
				payrollConfigs: { delete: { customerId } },
				taxesConfigs: { delete: { customerId } },
				taxTimelines: { delete: { customerId } },
				taxPasswords: { delete: { customerId } },
				secrets: { delete: { customerId } },
			})
			.update({
				nfseSettings: { create: nfseSettings },
				partners: { create: partners },
				payrollConfigs: { create: payrollConfigs },
				taxesConfigs: { create: taxesConfigs },
				taxNumbers: { create: taxNumbers },
				taxTimelines: { create: taxTimelines },
				taxPasswords: { create: taxPasswords },
				secrets: { create: secretsWithCustomerId },
			})

		// Create groups and departments on RocketChat (no need to do for each customer, only once)
		const [
			[rocketchatData, rocketchatError], //
			[invoicyData, invoicyError],
			[seafileData, seafileError],
			[disableInvoicyData, disableInvoicyError],
		] = await Promise.all([saveCustomersOnRocketChat(), saveCustomersOnInvoicy(), saveCustomersOnSeafile(), disableCustomersOnInvoicy()])

		if (!rocketchatData) throw rocketchatError
		if (!invoicyData) throw invoicyError
		if (!seafileData) throw seafileError
		if (!disableInvoicyData) throw disableInvoicyError

		return result
	})
