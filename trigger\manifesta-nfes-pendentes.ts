import { sendEvent } from '@/app/korok/fiscal/nf/recebidas/_actions/sendEvent'
import db from '@/db/db'
import { customers } from '@/db/schema/customers'
import { nfe } from '@/db/schema/nfe'
import { nfeResumo } from '@/db/schema/nfeResumo'
import { logger, schedules } from '@trigger.dev/sdk/v3'
import dayjs from 'dayjs'
import { and, eq, exists, gt, gte, notExists, sql } from 'drizzle-orm'

export const manifestaNfesPendentesTrigger = schedules.task({
	id: 'manifesta-nfes-pendentes',

	cron: '0 3 * * *', // every day at 3 am

	maxDuration: 3600 * 2, // 2 hours

	run: async () => {
		const nfses = db
			.select({ accessKey: nfe.accessKey, customerId: nfe.customerId })
			.from(nfe)
			.where(eq(nfeResumo.accessKey, nfe.accessKey))

		const data = await db
			.select({
				customerTitle: customers.title,
				customerId: nfeResumo.customerId,
				accessKey: nfeResumo.accessKey,
			})
			.from(nfeResumo)
			.innerJoin(customers, eq(nfeResumo.customerId, customers.id))
			.where(
				and(
					notExists(nfses),
					gte(nfeResumo.date, dayjs().subtract(179, 'days').toDate()),
					exists(
						db
							.select()
							.from(sql`vault.secrets`)
							.where(
								and(
									eq(sql`split_part(name, '-', 1)::uuid`, nfeResumo.customerId),
									eq(sql`split_part(name, '-', 2)`, 'icp-brasil-certificate'),
									gt(sql`(description::jsonb->>'expiration')::timestamp`, new Date()),
								),
							),
					),
				),
			)

		logger.log(`${data.length} resumos to process`)

		for (const [i, { customerId, accessKey, customerTitle }] of data.entries()) {
			logger.log(`Processing NF with access key ${accessKey} for customer ${customerTitle} (${customerId}) - ${i + 1} of ${data.length}`)

			const [result, resultError] = await sendEvent({ accessKey, customerId })

			if (!result) logger.error(resultError?.message ?? `Error sending event for NF ${accessKey}`)
		}
	},
})
