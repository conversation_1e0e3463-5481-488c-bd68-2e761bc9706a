import { expect, mock, test } from 'bun:test'

// Mock simples que funciona
const mockDb = {
	secrets: {
		select: mock().mockReturnValue({
			where: mock().mockResolvedValue([]),
		}),
	},
}

mock.module('@/src/db/db', () => ({ db: mockDb }))

const { getBankIntegrationData } = await import('./getBankIntegrationData')

test('retorna isEnabled: true quando certificado existe', async () => {
	const customerId = '123e4567-e89b-12d3-a456-************'

	mockDb.secrets.select().where.mockResolvedValueOnce([{ customerId }])

	const [result] = await getBankIntegrationData({ customerId })

	expect(result?.isEnabled).toBe(true)
})

test('retorna isEnabled: false quando certificado não existe', async () => {
	const customerId = '123e4567-e89b-12d3-a456-************'

	mockDb.secrets.select().where.mockResolvedValueOnce([])

	const [result] = await getBankIntegrationData({ customerId })

	expect(result?.isEnabled).toBe(false)
})
