import 'server-only'

import { dayjs } from '@/helpers/dayjs'
import { actionWithContext } from '@/lib/actions/actionWithContext'
import { getSupabaseAdmin } from '@/lib/supabase/admin-client'
import { db } from '@/src/db/db'

export const getData = actionWithContext.handler(async ({ ctx: { permissions, isManager, roles } }) => {
	const supabase = getSupabaseAdmin()

	const { data: secrets, error: secretsError } = await supabase
		.from('vault.decrypted_secrets')
		.select('id, name, description')
		.eq('description', 'icp-brasil-certificate')

	if (secretsError) {
		throw new Error(`Failed to fetch secrets: ${secretsError.message}`)
	}

	const customerIds = (secrets || [])
		.map(secret => secret.id) // id agora contém o customerId
		.filter(
			(customerId): customerId is string =>
				!!customerId && (isManager || roles.includes('bessa-team-law') || permissions.includes(customerId)),
		)

	if (customerIds.length === 0) return []
	const customers = await db.customers
		.select('id', 'title', {
			cnpj: q => q.taxNumbers.where({ type: 'CNPJ' }).get('taxNumber'),
		})
		.where({
			id: { in: customerIds },
			OR: [{ until: null }, { until: { gte: dayjs().toDate() } }],
		})
		.order('title')

	return customers.map(customer => {
		const secret = secrets.find(s => s.id === customer.id)
		let expiration: Date | null = null

		if (secret?.description) {
			try {
				const metadata = JSON.parse(secret.description)
				expiration = metadata.expiration ? new Date(metadata.expiration) : null
			} catch {
				// Ignore parsing errors
			}
		}

		return {
			id: customer.id,
			title: customer.title,
			cnpj: customer.cnpj,
			expiration,
		}
	})
})
