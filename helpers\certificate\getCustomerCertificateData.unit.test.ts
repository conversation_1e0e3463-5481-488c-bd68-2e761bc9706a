import { expect, test } from 'bun:test'

const testGetCustomerCertificateData = (customerId: string) => {
	const secrets = [
		{
			customerId: 'valid-customer',
			type: 'icp-brasil-certificate',
			expiration: new Date('2025-12-31'),
			data: 'dGVzdC1jZXJ0aWZpY2F0ZS1kYXRh',
		},
		{ customerId: 'valid-customer', type: 'icp-brasil-secret', expiration: null, data: 'test-passphrase' },
		{
			customerId: 'expired-customer',
			type: 'icp-brasil-certificate',
			expiration: new Date('2020-12-31'),
			data: 'dGVzdC1jZXJ0aWZpY2F0ZS1kYXRh',
		},
		{ customerId: 'expired-customer', type: 'icp-brasil-secret', expiration: null, data: 'test-passphrase' },
	]

	const cert = secrets.find(s => s.customerId === customerId && s.type === 'icp-brasil-certificate')
	const pass = secrets.find(s => s.customerId === customerId && s.type === 'icp-brasil-secret')

	if (!cert || !pass) {
		return [
			null,
			new Error(
				'O certificado digital deste cliente não foi encontrado. Verifique se o certificado está cadastrado e dentro do período de validade',
			),
		]
	}

	if (!cert.expiration || new Date(cert.expiration) < new Date()) {
		return [null, new Error('O certificado digital deste cliente está expirado. Verifique se há um certificado válido cadastrado')]
	}

	if (!cert.data || !pass.data) {
		return [null, new Error('Certificate data not found')]
	}

	return [{ certificateData: Buffer.from('mock-cert'), passphraseData: Buffer.from(pass.data) }, null]
}

test('Should return certificate data for valid customer with valid certificate', () => {
	const [result, error] = testGetCustomerCertificateData('valid-customer')

	expect(error).toBeNull()
	expect(result).not.toBeNull()
	if (result && typeof result === 'object' && 'certificateData' in result) {
		expect(result.certificateData).toBeInstanceOf(Buffer)
		expect(result.passphraseData).toBeInstanceOf(Buffer)
	}
})

test('Should throw error for customer without certificate', () => {
	const [result, error] = testGetCustomerCertificateData('nonexistent-customer')

	expect(result).toBeNull()
	expect(error).toBeInstanceOf(Error)
	if (error instanceof Error) {
		expect(error.message).toContain('não foi encontrado')
	}
})

test('Should throw error for customer with expired certificate', () => {
	const [result, error] = testGetCustomerCertificateData('expired-customer')

	expect(result).toBeNull()
	expect(error).toBeInstanceOf(Error)
	if (error instanceof Error) {
		expect(error.message).toContain('está expirado')
	}
})

test('Should validate certificate expiration correctly', () => {
	const validDate = new Date()
	validDate.setFullYear(validDate.getFullYear() + 1)

	expect(validDate > new Date()).toBe(true)

	const expiredDate = new Date()
	expiredDate.setFullYear(expiredDate.getFullYear() - 1)

	expect(expiredDate < new Date()).toBe(true)
})

test('Should handle missing certificate data', () => {
	const testWithMissingData = (customerId: string) => {
		const secrets = [
			{
				customerId,
				type: 'icp-brasil-certificate',
				expiration: new Date('2025-12-31'),
				data: '',
			},
			{
				customerId,
				type: 'icp-brasil-secret',
				expiration: null,
				data: 'test-passphrase',
			},
		]

		const customerSecrets = secrets.filter(s => s.customerId === customerId)
		const certificate = customerSecrets.find(s => s.type === 'icp-brasil-certificate')
		const passphrase = customerSecrets.find(s => s.type === 'icp-brasil-secret')

		if (!certificate?.data || !passphrase?.data) {
			return [null, new Error('Certificate data not found')]
		}

		return [{ certificateData: Buffer.from('test'), passphraseData: Buffer.from('test') }, null]
	}

	const [result, error] = testWithMissingData('test-customer')

	expect(result).toBeNull()
	expect(error).toBeInstanceOf(Error)
	if (error instanceof Error) {
		expect(error.message).toBe('Certificate data not found')
	}
})

test('Should require both certificate and passphrase', () => {
	const testWithOnlyPassphrase = (customerId: string) => {
		const secrets = [
			{
				customerId,
				type: 'icp-brasil-secret',
				expiration: null,
				data: 'test-passphrase',
			},
		]

		const customerSecrets = secrets.filter(s => s.customerId === customerId)
		const certificate = customerSecrets.find(s => s.type === 'icp-brasil-certificate')
		const passphrase = customerSecrets.find(s => s.type === 'icp-brasil-secret')

		if (!certificate || !passphrase) {
			return [
				null,
				new Error(
					'O certificado digital deste cliente não foi encontrado. Verifique se o certificado está cadastrado e dentro do período de validade',
				),
			]
		}

		return [{ certificateData: Buffer.from('test'), passphraseData: Buffer.from('test') }, null]
	}

	const [result, error] = testWithOnlyPassphrase('test-customer')

	expect(result).toBeNull()
	expect(error).toBeInstanceOf(Error)
	if (error instanceof Error) {
		expect(error.message).toContain('não foi encontrado')
	}
})

test('Should handle UUID validation in input', () => {
	const validUuid = 'f47ac10b-58cc-4372-a567-0e02b2c3d479'
	expect(validUuid).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)

	const invalidUuid = 'not-a-uuid'
	expect(invalidUuid).not.toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
})
